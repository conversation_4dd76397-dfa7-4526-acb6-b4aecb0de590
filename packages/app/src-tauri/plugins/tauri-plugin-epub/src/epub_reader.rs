use anyhow::{Context, Result};
use epub::doc::EpubDoc;
use std::collections::HashMap;
use std::path::Path;
use tiktoken_rs::o200k_base;

#[derive(Debug, Clone)]
pub struct EpubChapter {
    pub title: String,
    pub content: String,
    pub order: usize,
}

#[derive(Debug)]
pub struct EpubContent {
    pub title: String,
    pub author: String,
    pub chapters: Vec<EpubChapter>,
}

pub struct EpubReader {
    tokenizer: tiktoken_rs::CoreBPE,
}

impl EpubReader {
    pub fn new() -> Result<Self> {
        let tokenizer = o200k_base().context("Failed to initialize tiktoken tokenizer")?;
        Ok(Self { tokenizer })
    }

    /// 读取 EPUB 文件并提取所有文本内容
    pub fn read_epub<P: AsRef<Path>>(&self, path: P) -> Result<EpubContent> {
        let mut doc = EpubDoc::new(path).context("Failed to open EPUB file")?;

        // 获取书籍基本信息
        let title = doc
            .mdata("title")
            .unwrap_or_else(|| "Unknown Title".to_string());
        let author = doc
            .mdata("creator")
            .or_else(|| doc.mdata("author"))
            .unwrap_or_else(|| "Unknown Author".to_string());

        log::info!("Reading EPUB: {} by {}", title, author);
        log::info!("EPUB spine length: {}", doc.spine.len());
        log::info!("EPUB resources count: {}", doc.resources.len());

        let mut chapters = Vec::new();
        let spine_len = doc.get_num_pages();

        // 确保从第一页开始
        doc.set_current_page(0);

        // 遍历所有章节
        for i in 0..spine_len {
            // 设置当前页面
            if !doc.set_current_page(i) {
                log::warn!("无法设置到第 {} 页", i);
                continue;
            }

            // 获取当前页面的内容
            match doc.get_current_str() {
                Some((content, mime_type)) => {
                    log::debug!("处理第 {} 页，MIME类型: {}", i, mime_type);

                    // 只处理文本内容（XHTML/HTML）
                    if mime_type.contains("html") || mime_type.contains("xml") {
                        let chapter_title = self
                            .extract_chapter_title(&content)
                            .or_else(|| doc.get_current_id().map(|id| id.to_string()))
                            .unwrap_or_else(|| format!("Chapter {}", i + 1));

                        let text_content = self.extract_text_content(&content)?;

                        if !text_content.trim().is_empty() {
                            log::debug!("添加章节: {} ({}字符)", chapter_title, text_content.len());
                            chapters.push(EpubChapter {
                                title: chapter_title,
                                content: text_content,
                                order: i,
                            });
                        } else {
                            log::debug!("跳过空章节: {}", chapter_title);
                        }
                    } else {
                        log::debug!(
                            "跳过非文本资源: {} (MIME: {})",
                            doc.get_current_id().unwrap_or("unknown".to_string()),
                            mime_type
                        );
                    }
                }
                None => {
                    log::warn!("无法获取第 {} 页的内容", i);
                }
            }
        }

        log::info!("成功提取 {} 个章节", chapters.len());

        Ok(EpubContent {
            title,
            author,
            chapters,
        })
    }

    /// 从 HTML 内容中提取纯文本
    fn extract_text_content(&self, html: &str) -> Result<String> {
        let mut text = html.to_string();

        // 首先移除脚本和样式标签（包括其内容）
        let script_style_patterns = [
            r"(?is)<script[^>]*>.*?</script>",
            r"(?is)<style[^>]*>.*?</style>",
            r"(?is)<!--.*?-->", // 移除HTML注释
        ];

        for pattern in &script_style_patterns {
            let re = regex::Regex::new(pattern)?;
            text = re.replace_all(&text, " ").to_string();
        }

        // 处理块级元素，在它们周围添加换行
        let block_elements = [
            r"(?i)</?(div|p|h[1-6]|section|article|chapter|br)[^>]*>",
            r"(?i)</?(ul|ol|li|dl|dt|dd)[^>]*>",
            r"(?i)</?(table|tr|td|th)[^>]*>",
            r"(?i)</?(header|footer|nav|main|aside)[^>]*>",
        ];

        for pattern in &block_elements {
            let re = regex::Regex::new(pattern)?;
            text = re.replace_all(&text, "\n").to_string();
        }

        // 移除所有剩余的HTML标签
        let re = regex::Regex::new(r"<[^>]+>")?;
        text = re.replace_all(&text, " ").to_string();

        // 解码HTML实体
        text = self.decode_html_entities(&text);

        // 清理空白字符
        let re = regex::Regex::new(r"\s+")?;
        text = re.replace_all(&text, " ").to_string();

        // 清理多余的换行
        let re = regex::Regex::new(r"\n\s*\n")?;
        text = re.replace_all(&text, "\n").to_string();

        Ok(text.trim().to_string())
    }

    /// 提取章节标题
    fn extract_chapter_title(&self, html: &str) -> Option<String> {
        // 尝试从标题标签中提取标题，按优先级排序
        let title_patterns = [
            r"(?is)<title[^>]*>(.*?)</title>",
            r"(?is)<h1[^>]*>(.*?)</h1>",
            r"(?is)<h2[^>]*>(.*?)</h2>",
            r"(?is)<h3[^>]*>(.*?)</h3>",
            r"(?is)<h4[^>]*>(.*?)</h4>",
            // 查找带有特定class的元素
            r#"(?is)<[^>]*class=\"[^\"]*title[^\"]*\"[^>]*>(.*?)</[^>]*>"#,
            r#"(?is)<[^>]*class=\"[^\"]*chapter[^\"]*\"[^>]*>(.*?)</[^>]*>"#,
        ];

        for pattern in &title_patterns {
            if let Ok(re) = regex::Regex::new(pattern) {
                if let Some(captures) = re.captures(html) {
                    if let Some(title_match) = captures.get(1) {
                        let title = title_match.as_str();
                        let clean_title = self.extract_text_content(title).unwrap_or_default();
                        let clean_title = clean_title.trim();

                        // 过滤掉太短或太长的标题
                        if !clean_title.is_empty()
                            && clean_title.len() >= 1
                            && clean_title.len() <= 200
                        {
                            // 清理常见的无用标题
                            if !clean_title.to_lowercase().contains("untitled")
                                && !clean_title.to_lowercase().contains("unnamed")
                                && !clean_title
                                    .chars()
                                    .all(|c| c.is_numeric() || c.is_whitespace())
                            {
                                return Some(clean_title.to_string());
                            }
                        }
                    }
                }
            }
        }

        None
    }

    /// HTML实体解码
    fn decode_html_entities(&self, text: &str) -> String {
        let entities: HashMap<&str, &str> = [
            // 基本HTML实体
            ("&amp;", "&"),
            ("&lt;", "<"),
            ("&gt;", ">"),
            ("&quot;", "\""),
            ("&apos;", "'"),
            ("&nbsp;", " "),
            // 常见标点符号
            ("&#8220;", "\u{201C}"),
            ("&ldquo;", "\u{201C}"),
            ("&#8221;", "\u{201D}"),
            ("&rdquo;", "\u{201D}"),
            ("&#8216;", "\u{2018}"),
            ("&lsquo;", "\u{2018}"),
            ("&#8217;", "\u{2019}"),
            ("&rsquo;", "\u{2019}"),
            ("&#8212;", "\u{2014}"),
            ("&mdash;", "\u{2014}"),
            ("&#8211;", "\u{2013}"),
            ("&ndash;", "\u{2013}"),
            ("&#8230;", "\u{2026}"),
            ("&hellip;", "\u{2026}"),
            // 其他常见实体
            ("&copy;", "\u{00A9}"),
            ("&reg;", "\u{00AE}"),
            ("&trade;", "\u{2122}"),
            ("&deg;", "\u{00B0}"),
            ("&middot;", "\u{00B7}"),
            ("&bull;", "\u{2022}"),
            ("&sect;", "\u{00A7}"),
            ("&para;", "\u{00B6}"),
        ]
        .iter()
        .cloned()
        .collect();

        let mut result = text.to_string();

        // 替换命名实体
        for (entity, replacement) in entities {
            result = result.replace(entity, replacement);
        }

        // 处理数字实体 &#数字; 格式
        let re = regex::Regex::new(r"&#(\d+);").unwrap();
        result = re
            .replace_all(&result, |caps: &regex::Captures| {
                if let Ok(num) = caps[1].parse::<u32>() {
                    if let Some(ch) = char::from_u32(num) {
                        return ch.to_string();
                    }
                }
                caps[0].to_string() // 如果无法解析，保持原样
            })
            .to_string();

        // 处理十六进制实体 &#x十六进制; 格式
        let re = regex::Regex::new(r"&#x([0-9a-fA-F]+);").unwrap();
        result = re
            .replace_all(&result, |caps: &regex::Captures| {
                if let Ok(num) = u32::from_str_radix(&caps[1], 16) {
                    if let Some(ch) = char::from_u32(num) {
                        return ch.to_string();
                    }
                }
                caps[0].to_string() // 如果无法解析，保持原样
            })
            .to_string();

        result
    }

    /// 将长文本分割成较小的块以便向量化
    /// 按行分割，每个块的 token 数量控制在 min_tokens 到 max_tokens 之间
    pub fn chunk_text(&self, text: &str, _max_chunk_size: usize, _overlap: usize) -> Vec<String> {
        self.chunk_text_by_tokens(text, 256, 400)
    }

    /// 按 token 数量和行边界分割文本，带20%重叠
    pub fn chunk_text_by_tokens(
        &self,
        text: &str,
        min_tokens: usize,
        max_tokens: usize,
    ) -> Vec<String> {
        let safe_max_tokens = max_tokens.min(256);
        let overlap_tokens = (safe_max_tokens as f32 * 0.2) as usize;

        let lines: Vec<&str> = text.lines().collect();
        let mut chunks = Vec::new();
        let mut all_lines_with_tokens: Vec<(String, usize)> = Vec::new();

        // 预处理所有行，计算token数量
        for line in lines {
            let line = line.trim();
            if !line.is_empty() {
                let tokens = self.estimate_tokens(line);
                all_lines_with_tokens.push((line.to_string(), tokens));
            }
        }

        if all_lines_with_tokens.is_empty() {
            return chunks;
        }

        let mut start_idx = 0;

        while start_idx < all_lines_with_tokens.len() {
            let mut current_chunk = Vec::new();
            let mut current_tokens = 0;
            let mut end_idx = start_idx;

            // 从start_idx开始，尽可能多地添加行，直到达到safe_max_tokens
            while end_idx < all_lines_with_tokens.len() {
                let (line, line_tokens) = &all_lines_with_tokens[end_idx];

                // 如果单行就超过限制，需要特殊处理
                if *line_tokens > safe_max_tokens {
                    // 如果当前块不为空，先保存
                    if !current_chunk.is_empty() {
                        chunks.push(current_chunk.join("\n"));
                        current_chunk.clear();
                        current_tokens = 0;
                    }

                    // 分割这一行
                    let line_chunks = self.split_long_line(line, min_tokens, safe_max_tokens);
                    chunks.extend(line_chunks);
                    end_idx += 1;
                    break;
                }

                // 如果添加这一行会超过限制，并且当前块已经达到最小要求
                if current_tokens + line_tokens > safe_max_tokens && current_tokens >= min_tokens {
                    break;
                }

                current_chunk.push(line.clone());
                current_tokens += line_tokens;
                end_idx += 1;
            }

            // 保存当前块（如果有内容且满足最小要求）
            if !current_chunk.is_empty() && current_tokens >= min_tokens.min(100) {
                chunks.push(current_chunk.join("\n"));
            }

            // 计算下一个块的起始位置（带重叠）
            if end_idx >= all_lines_with_tokens.len() {
                break;
            }

            // 找到重叠的起始位置
            let mut overlap_start = start_idx;
            let mut overlap_tokens_count = 0;

            for i in (start_idx..end_idx).rev() {
                overlap_tokens_count += all_lines_with_tokens[i].1;
                if overlap_tokens_count >= overlap_tokens {
                    overlap_start = i;
                    break;
                }
            }

            start_idx = overlap_start.max(start_idx + 1);
        }

        chunks
    }

    /// 专门用于 Markdown 文件的智能分块方法
    /// 考虑 Markdown 格式特性：标题层级、段落边界、代码块等
    pub fn chunk_md_file(&self, md_content: &str, min_tokens: usize, max_tokens: usize) -> Vec<String> {
        let safe_max_tokens = max_tokens.min(400);
        let overlap_tokens = (safe_max_tokens as f32 * 0.2) as usize;
        
        // 首先尝试按 Markdown 结构分块
        if let Some(structured_chunks) = self.chunk_by_markdown_structure(md_content, min_tokens, safe_max_tokens) {
            return structured_chunks;
        }
        
        // 如果结构化分块失败，回退到标准文本分块
        log::debug!("Markdown structured chunking failed, falling back to text chunking");
        self.chunk_text_by_tokens(md_content, min_tokens, safe_max_tokens)
    }
    
    /// 按 Markdown 结构进行智能分块
    fn chunk_by_markdown_structure(&self, md_content: &str, min_tokens: usize, max_tokens: usize) -> Option<Vec<String>> {
        let lines: Vec<&str> = md_content.lines().collect();
        if lines.is_empty() {
            return Some(vec![]);
        }
        
        let mut chunks = Vec::new();
        let mut current_section = Vec::new();
        let mut current_tokens = 0;
        let overlap_tokens = (max_tokens as f32 * 0.2) as usize;
        
        for (i, line) in lines.iter().enumerate() {
            let line = line.trim();
            let line_tokens = self.estimate_tokens(line);
            
            // 检查是否是 Markdown 标题
            let is_header = line.starts_with('#') && line.len() > 1;
            let header_level = if is_header {
                line.chars().take_while(|&c| c == '#').count()
            } else {
                0
            };
            
            // 如果遇到标题，且当前段落已有内容，考虑分块
            if is_header && !current_section.is_empty() && current_tokens >= min_tokens {
                // 检查如果添加这个标题会不会超过限制
                if current_tokens + line_tokens > max_tokens {
                    // 保存当前段落
                    chunks.push(current_section.join("\n"));
                    
                    // 准备重叠内容
                    let overlap_content = self.prepare_overlap_content(&current_section, overlap_tokens);
                    current_section = overlap_content;
                    current_tokens = current_section.iter()
                        .map(|l| self.estimate_tokens(l))
                        .sum();
                }
            }
            
            // 检查代码块边界
            if line.starts_with("```") && !current_section.is_empty() {
                // 代码块应该保持完整，如果当前块很大，先分块
                if current_tokens >= min_tokens && current_tokens + line_tokens > max_tokens {
                    chunks.push(current_section.join("\n"));
                    current_section.clear();
                    current_tokens = 0;
                }
            }
            
            // 添加当前行
            if !line.is_empty() || current_section.len() > 0 {  // 保留非空行或作为段落分隔
                current_section.push(line.to_string());
                current_tokens += line_tokens;
            }
            
            // 检查是否达到最大 token 限制
            if current_tokens > max_tokens && current_section.len() > 1 {
                // 需要分块，但尽量在合适的边界
                if let Some(split_point) = self.find_best_split_point(&current_section, min_tokens, max_tokens) {
                    let chunk_lines = current_section[..split_point].to_vec();
                    chunks.push(chunk_lines.join("\n"));
                    
                    // 准备重叠内容
                    let overlap_content = self.prepare_overlap_content(&chunk_lines, overlap_tokens);
                    let remaining_lines = current_section[split_point..].to_vec();
                    current_section = [overlap_content, remaining_lines].concat();
                    current_tokens = current_section.iter()
                        .map(|l| self.estimate_tokens(l))
                        .sum();
                } else {
                    // 如果找不到合适的分割点，强制分割
                    let mid = current_section.len() / 2;
                    let chunk_lines = current_section[..mid].to_vec();
                    chunks.push(chunk_lines.join("\n"));
                    current_section = current_section[mid..].to_vec();
                    current_tokens = current_section.iter()
                        .map(|l| self.estimate_tokens(l))
                        .sum();
                }
            }
        }
        
        // 处理最后一个段落
        if !current_section.is_empty() && current_tokens >= min_tokens.min(50) {
            chunks.push(current_section.join("\n"));
        }
        
        // 过滤掉过短的块
        let filtered_chunks: Vec<String> = chunks.into_iter()
            .filter(|chunk| !chunk.trim().is_empty() && self.estimate_tokens(chunk) >= min_tokens.min(30))
            .collect();
        
        if filtered_chunks.is_empty() {
            None
        } else {
            Some(filtered_chunks)
        }
    }
    
    /// 寻找最佳的分割点（段落结束、列表项等）
    fn find_best_split_point(&self, lines: &[String], min_tokens: usize, max_tokens: usize) -> Option<usize> {
        let mut best_point = None;
        let mut accumulated_tokens = 0;
        
        for (i, line) in lines.iter().enumerate() {
            accumulated_tokens += self.estimate_tokens(line);
            
            // 如果还没达到最小要求，继续
            if accumulated_tokens < min_tokens {
                continue;
            }
            
            // 检查是否是好的分割点
            let is_good_split = line.trim().is_empty()  // 空行
                || line.trim().ends_with('.')          // 句号结尾
                || line.trim().ends_with('!')          // 感叹号结尾
                || line.trim().ends_with('?')          // 问号结尾
                || line.starts_with('#')               // 标题
                || line.starts_with('-')               // 列表项
                || line.starts_with('*')               // 列表项
                || line.starts_with("```");            // 代码块
            
            if is_good_split {
                best_point = Some(i + 1);
                
                // 如果已经超过理想长度，就在这里分割
                if accumulated_tokens >= max_tokens * 3 / 4 {
                    break;
                }
            }
            
            // 如果超过最大限制，必须分割
            if accumulated_tokens >= max_tokens {
                break;
            }
        }
        
        best_point
    }
    
    /// 准备重叠内容
    fn prepare_overlap_content(&self, lines: &[String], max_overlap_tokens: usize) -> Vec<String> {
        let mut overlap_content = Vec::new();
        let mut overlap_tokens = 0;
        
        // 从末尾开始收集重叠内容
        for line in lines.iter().rev() {
            let line_tokens = self.estimate_tokens(line);
            if overlap_tokens + line_tokens <= max_overlap_tokens {
                overlap_content.insert(0, line.clone());
                overlap_tokens += line_tokens;
            } else {
                break;
            }
        }
        
        overlap_content
    }

    /// 将一行很长的文本进一步分割为若干子块，优先按句子，其次按字符带重叠
    fn split_long_line(&self, line: &str, min_tokens: usize, max_tokens: usize) -> Vec<String> {
        let sentences = self.split_into_sentences(line);
        // 如果无法有效分句，退化为按字符分割
        if sentences.len() <= 1 {
            return self.split_by_characters(line, max_tokens);
        }

        let overlap_tokens = (max_tokens as f32 * 0.2) as usize;
        let mut chunks: Vec<String> = Vec::new();
        let mut current_chunk: Vec<String> = Vec::new();
        let mut current_tokens: usize = 0;

        for sentence in sentences {
            let tokens = self.estimate_tokens(&sentence);
            if tokens > max_tokens {
                // 先把已有的块输出
                if !current_chunk.is_empty() {
                    chunks.push(current_chunk.join(""));
                    current_chunk.clear();
                    current_tokens = 0;
                }
                // 超长句子按字符切分
                let char_chunks = self.split_by_characters_with_overlap(
                    &sentence,
                    max_tokens,
                    (max_tokens as f32 * 0.2 * 0.8) as usize,
                );
                chunks.extend(char_chunks);
                continue;
            }

            if current_tokens + tokens > max_tokens && current_tokens >= min_tokens {
                // 关闭当前块
                chunks.push(current_chunk.join(""));
                // 准备重叠：从尾部回退若干句子形成重叠
                let mut overlap_vec: Vec<String> = Vec::new();
                let mut acc = 0usize;
                for s in current_chunk.iter().rev() {
                    let t = self.estimate_tokens(s);
                    if acc + t > overlap_tokens {
                        break;
                    }
                    acc += t;
                    overlap_vec.push(s.clone());
                }
                overlap_vec.reverse();
                current_chunk = overlap_vec;
                current_tokens = current_chunk.iter().map(|s| self.estimate_tokens(s)).sum();
            }

            current_chunk.push(sentence);
            current_tokens += tokens;
        }

        if !current_chunk.is_empty() {
            chunks.push(current_chunk.join(""));
        }

        chunks
    }

    /// 将文本分割成句子
    fn split_into_sentences(&self, text: &str) -> Vec<String> {
        // 按中文和英文的句子结束符分割
        let sentence_endings = ['。', '！', '？', '.', '!', '?'];
        let mut sentences = Vec::new();
        let mut current_sentence = String::new();

        for ch in text.chars() {
            current_sentence.push(ch);

            if sentence_endings.contains(&ch) {
                sentences.push(current_sentence.trim().to_string());
                current_sentence.clear();
            }
        }

        // 添加剩余的文本
        if !current_sentence.trim().is_empty() {
            sentences.push(current_sentence.trim().to_string());
        }

        sentences.into_iter().filter(|s| !s.is_empty()).collect()
    }

    /// 按字符数量分割文本（最后的手段）
    fn split_by_characters(&self, text: &str, max_tokens: usize) -> Vec<String> {
        let overlap_chars = (max_tokens as f32 * 0.2 * 0.8) as usize; // 20%重叠，0.8是安全系数
        self.split_by_characters_with_overlap(text, max_tokens, overlap_chars)
    }

    /// 按字符数量分割文本，带重叠
    fn split_by_characters_with_overlap(
        &self,
        text: &str,
        max_tokens: usize,
        overlap_chars: usize,
    ) -> Vec<String> {
        let chars: Vec<char> = text.chars().collect();
        let mut chunks = Vec::new();
        let chunk_size = (max_tokens as f32 * 0.8) as usize; // 留一些余量

        if chars.len() <= chunk_size {
            if !text.trim().is_empty() {
                chunks.push(text.to_string());
            }
            return chunks;
        }

        let mut start = 0;

        while start < chars.len() {
            let end = (start + chunk_size).min(chars.len());
            let chunk_chars = &chars[start..end];
            let chunk: String = chunk_chars.iter().collect();

            if !chunk.trim().is_empty() {
                chunks.push(chunk);
            }

            // 计算下一个块的起始位置（带重叠）
            if end >= chars.len() {
                break;
            }

            start = (end - overlap_chars).max(start + 1);
        }

        chunks
    }

    pub fn estimate_tokens(&self, text: &str) -> usize {
        self.tokenizer.encode_with_special_tokens(text).len()
    }
}
