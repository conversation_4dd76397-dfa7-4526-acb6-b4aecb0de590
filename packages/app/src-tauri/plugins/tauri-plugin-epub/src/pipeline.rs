use anyhow::{Context, Result};
use serde::Serialize;
use std::fs;
use std::path::{Path, PathBuf};

use crate::database::{DocumentChunk, VectorDatabase};
use crate::epub_reader::{<PERSON>pub<PERSON><PERSON>er, EpubContent};
use crate::vectorizer::{TextVectorizer, VectorizerConfig};
use crate::parse_toc::{parse_toc_file, find_toc_ncx_in_mdbook, flatten_toc, FlatTocNode};
use epub2mdbook::convert_epub_to_mdbook;
use serde::Deserialize;

/// Options for processing an EPUB into a vector DB
#[derive(Debug, Clone)]
pub struct ProcessOptions {
    pub dimension: usize,
    pub batch_size: Option<usize>,
    pub vectorizer: VectorizerConfig,
}

// 不再提供默认实现，要求调用方显式传入配置

/// Processing summary
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ProcessReport {
    pub db_path: PathBuf,
    pub book_title: String,
    pub book_author: String,
    pub total_chunks: usize,
    pub vector_dimension: usize,
}

#[derive(Debug, <PERSON>lone, Serialize)]
pub struct ProgressUpdate {
    pub current: usize,
    pub total: usize,
    pub percent: f32,
    pub chapter_title: String,
    pub chunk_index: usize,
}

#[derive(Debug, Clone, Deserialize, Serialize, Default)]
struct MetadataPerson {
    #[serde(default)]
    name: Option<String>,
    #[serde(default)]
    role: Option<String>,
    #[serde(default, rename = "sortAs")]
    sort_as: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(untagged)]
enum AuthorField {
    Person(MetadataPerson),
    List(Vec<MetadataPerson>),
    String(String),
}

#[derive(Debug, Clone, Deserialize, Serialize, Default)]
struct BookMetadataFile {
    #[serde(default)]
    title: Option<String>,
    #[serde(default)]
    language: Option<String>,
    #[serde(default)]
    published: Option<String>,
    #[serde(default)]
    publisher: Option<String>,
    #[serde(default)]
    author: Option<AuthorField>,
    #[serde(default)]
    base_dir: Option<String>,
}

/// Core pipeline: book_dir -> locate book.epub -> parse -> write chapters -> vectorize -> persist to SQLite
pub async fn process_epub_to_db<P: AsRef<Path>, F>(
    book_dir: P,
    opts: ProcessOptions,
    mut on_progress: Option<F>,
) -> Result<ProcessReport>
where
    F: FnMut(ProgressUpdate) + Send,
{
    let book_dir = book_dir.as_ref();
    let epub_path = book_dir.join("book.epub");
    let db_path = book_dir.join("vectors.sqlite");

    if !epub_path.exists() {
        anyhow::bail!("EPUB not found: {:?}", epub_path);
    }

    log::info!("Reading EPUB: {:?}", epub_path);
    let reader = EpubReader::new().context("Failed to initialize EPUB reader")?;
    let epub_content = reader.read_epub(&epub_path).context("Failed to read EPUB")?;

    log::info!(
        "Loaded book: {} (author: {}), chapters: {}",
        epub_content.title,
        epub_content.author,
        epub_content.chapters.len()
    );

    // Step 1: Convert EPUB to MDBook format
    let mdbook_dir = book_dir.join("mdbook");
    log::info!("Converting EPUB to MDBook format at: {:?}", mdbook_dir);
    
    if !mdbook_dir.exists() {
        fs::create_dir_all(&mdbook_dir).context("Failed to create mdbook directory")?;
    }
    
    // Check if conversion is needed (compare file timestamps)
    // let need_conversion = if mdbook_dir.join("book").join("src").exists() {
    //     let epub_time = epub_path.metadata()
    //         .map(|m| m.modified().unwrap_or(std::time::SystemTime::UNIX_EPOCH))
    //         .unwrap_or(std::time::SystemTime::UNIX_EPOCH);
        
    //     let mdbook_time = mdbook_dir.metadata()
    //         .map(|m| m.modified().unwrap_or(std::time::SystemTime::UNIX_EPOCH))
    //         .unwrap_or(std::time::SystemTime::UNIX_EPOCH);
        
    //     epub_time > mdbook_time
    // } else {
    //     true
    // };
    let need_conversion = true;
    
    if need_conversion {
        log::info!("Converting EPUB to MDBook (EPUB is newer or MDBook doesn't exist)");
        convert_epub_to_mdbook(&epub_path, &mdbook_dir, true)
            .map_err(|e| anyhow::anyhow!("Failed to convert EPUB to MDBook: {}", e))?;
        log::info!("EPUB to MDBook conversion completed");
    } else {
        log::info!("MDBook is up-to-date, skipping conversion");
    }

    // Step 2: Parse and flatten TOC
    log::info!("Parsing TOC structure...");
    let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)
        .context("TOC file (toc.ncx) not found in MDBook directory")?;
    
    log::info!("Found TOC file at: {:?}", toc_path);
    let toc_nodes = parse_toc_file(&toc_path)
        .map_err(|e| anyhow::anyhow!("Failed to parse TOC file: {}", e))?;
    
    let flat_toc = flatten_toc(&toc_nodes);
    log::info!("TOC parsing completed: {} entries found", flat_toc.len());

    // Write chapters to text files under book_dir/chapters (keep for backward compatibility)
    let chapters_dir = book_dir.join("chapters");
    if !chapters_dir.exists() {
        fs::create_dir_all(&chapters_dir).context("Failed to create chapters directory")?;
    }

    for chapter in &epub_content.chapters {
        let safe_title = sanitize_filename(&chapter.title);
        let filename = format!("{:03}-{}.txt", chapter.order + 1, safe_title);
        let path = chapters_dir.join(filename);
        fs::write(&path, &chapter.content).with_context(|| format!("Failed to write chapter: {:?}", path))?;
    }
    log::info!(
        "章节落盘完成：写入目录 {:?}，章节数 {}",
        chapters_dir,
        epub_content.chapters.len()
    );

    // Step 3: Process MD files grouped by md_src to avoid duplicate chunking
    let mut all_chunks = Vec::new();
    let mdbook_src_dir = mdbook_dir.join("book").join("src");
    log::info!("Processing MD files for chunking (dedup by md_src)...");

    // 获取toc.ncx文件所在的目录，MD文件路径都是相对于它的
    let toc_base_dir = toc_path.parent()
        .ok_or_else(|| anyhow::anyhow!("Cannot get parent directory of TOC file"))?;

    // Step 2.5: Write metadata.md combining metadata.json and TOC summary
    if let Err(e) = write_metadata_markdown(book_dir, &epub_content, &flat_toc, toc_base_dir) {
        log::warn!("生成 metadata.md 失败：{}", e);
    } else {
        log::info!("已生成 metadata.md（用于模型提示）");
    }

    // 构建 md_src -> 该文件相关的 TOC 节点列表（包含其在全局扁平 TOC 中的索引）
    let mut md_groups: std::collections::BTreeMap<String, Vec<(usize, FlatTocNode)>> = std::collections::BTreeMap::new();
    for (idx, n) in flat_toc.iter().cloned().enumerate() {
        md_groups.entry(n.md_src.clone()).or_default().push((idx, n));
    }

    // 依次处理每个唯一 md_src 文件
    for (md_src, mut nodes) in md_groups {
        let md_file_path = toc_base_dir.join(&md_src);
        if !md_file_path.exists() {
            log::warn!("MD file not found: {:?} (relative to TOC: {}), skipping", md_file_path, md_src);
            continue;
        }

        let md_content = fs::read_to_string(&md_file_path)
            .with_context(|| format!("Failed to read MD file: {:?}", md_file_path))?;
        if md_content.trim().is_empty() { continue; }

        // 使用 Markdown 感知分片（更稳定的结构边界）
        let chunks = reader.chunk_md_file(&md_content, 50, 400);
        let total_chunks = chunks.len();
        if total_chunks == 0 { continue; }

        // 为该文件内的 TOC 节点估计在文档中的起始位置（字符索引）
        // 规则：优先用标题文本匹配（从前往后查找，保证单调递增）；找不到则先置空，稍后插值/回退
        nodes.sort_by_key(|(_, n)| n.play_order);
        let mut starts: Vec<(usize /*toc_index*/, usize /*start_pos*/)> = Vec::new();
        let mut search_from = 0usize;
        for (toc_index, n) in &nodes {
            let title = n.title.trim();
            if title.is_empty() { continue; }
            if let Some(pos) = md_content[search_from..].find(title) {
                let global_pos = search_from + pos;
                starts.push((*toc_index, global_pos));
                search_from = global_pos + title.len();
            } else {
                // 记为未命中，稍后用估计位置填充
            }
        }
        // 补全未命中的，用等分估计位置，保证单调递增
        if starts.len() < nodes.len() {
            let content_len = md_content.len();
            let mut est_starts: Vec<(usize, usize)> = Vec::new();
            for (i, (toc_index, _n)) in nodes.iter().enumerate() {
                let est = (i as f32 / nodes.len() as f32 * content_len as f32) as usize;
                est_starts.push((*toc_index, est));
            }
            starts = est_starts;
        }
        // 规范化：按起点排序并裁剪到 [0, len)
        starts.sort_by_key(|(_, s)| *s);
        let content_len = md_content.len();
        for s in &mut starts { s.1 = s.1.min(content_len); }

        // 计算每个 TOC 节点在该文件中的区间 [start, end)
        let mut intervals: Vec<(usize /*toc_index*/, usize /*start*/, usize /*end*/)> = Vec::new();
        for i in 0..starts.len() {
            let (toc_idx, s) = starts[i];
            let e = if i + 1 < starts.len() { starts[i + 1].1 } else { content_len };
            intervals.push((toc_idx, s, e));
        }

        // 以“等长估计”给每个 chunk 一个起始位置，用于分配区间
        for (k, chunk_content) in chunks.into_iter().enumerate() {
            if chunk_content.trim().is_empty() { continue; }
            let est_pos = ((k as f32 / total_chunks as f32) * content_len as f32) as usize;
            // 找到所属区间（最后一个 start <= est_pos）
            let mut picked: Option<usize> = None;
            for (toc_idx, s, e) in &intervals {
                if est_pos >= *s && est_pos < *e { picked = Some(*toc_idx); break; }
            }
            // 若未命中，默认归到最后一个
            let toc_index = picked.unwrap_or_else(|| intervals.last().map(|x| x.0).unwrap_or(0));
            let n = &flat_toc[toc_index];

            let chunk_idx_in_toc = k; // 在该 md 文件中的顺序（近似代替）
            let total_chunks_in_toc = total_chunks; // 同一文件内的总块数（近似代替）

            all_chunks.push((
                n.clone(),
                toc_index,
                chunk_idx_in_toc,
                total_chunks_in_toc,
                chunk_content,
            ));
        }
    }

    log::info!("MD文件分块完成：总块数 {}（用于向量化）", all_chunks.len());

    // Open DB and try vec0
    let mut db =
        VectorDatabase::new(&db_path, opts.dimension).context("Failed to open/create database")?;
    if let Err(e) = db.initialize_vec_table() {
        log::warn!(
            "sqlite-vec unavailable, fallback to standard storage: {}",
            e
        );
    }

    // 统一使用真实/本地 API 向量化器
    let vectorizer = TextVectorizer::new(opts.vectorizer.clone()).await?;
    let mut batch = Vec::<DocumentChunk>::new();
    let batch_size = opts.batch_size.unwrap_or(10);
    let total = all_chunks.len();

    log::info!("开始向量化与入库：总块数 {}，批大小 {}", total, batch_size);

    for (i, (toc_node, toc_index, chunk_idx_in_toc, total_chunks_in_toc, chunk_content)) in all_chunks.iter().enumerate() {
        let progress = if total > 0 { ((i as f32 + 1.0) / total as f32) * 100.0 } else { 100.0 };
        log::info!(
            "向量化进度 {}/{} ({:.1}%) —— TOC: {} (深度: {}) 块序: {}/{}",
            i + 1,
            total,
            progress,
            toc_node.title,
            toc_node.depth,
            chunk_idx_in_toc + 1,
            total_chunks_in_toc
        );

        let embedding = vectorizer.vectorize_text(&chunk_content).await?;

        batch.push(DocumentChunk {
            id: None,
            book_title: epub_content.title.clone(),
            book_author: epub_content.author.clone(),
            chapter_title: toc_node.title.clone(),
            chapter_order: toc_node.play_order as usize,
            chunk_text: chunk_content.clone(),
            chunk_order: *chunk_idx_in_toc,
            embedding,
            
            // TOC导航字段
            md_src: toc_node.md_src.clone(),
            toc_depth: toc_node.depth,
            toc_id: toc_node.id.clone(),
            toc_index: *toc_index,
            
            // 分块位置字段
            chunk_index_in_toc: *chunk_idx_in_toc,
            total_chunks_in_toc: *total_chunks_in_toc,
            
            // 全局位置字段
            global_chunk_index: i,
        });

        if batch.len() >= batch_size {
            db.insert_chunks_batch(&batch)
                .context("Failed to insert chunk batch")?;
            let inserted = i + 1;
            let progress = if total > 0 { (inserted as f32 / total as f32) * 100.0 } else { 100.0 };
            log::info!(
                "批量入库成功：已处理 {}/{} ({:.1}%)",
                inserted,
                total,
                progress
            );
            batch.clear();
        }

        if let Some(cb) = on_progress.as_mut() {
            cb(ProgressUpdate {
                current: i + 1,
                total,
                percent: progress,
                chapter_title: toc_node.title.clone(),
                chunk_index: *chunk_idx_in_toc,
            });
        }
    }

    if !batch.is_empty() {
        db.insert_chunks_batch(&batch)
            .context("Failed to insert final batch")?;
    }
    log::info!("向量化与入库完成：共计 {} 块（100.0%）", total);

    Ok(ProcessReport {
        db_path,
        book_title: epub_content.title,
        book_author: epub_content.author,
        total_chunks: all_chunks.len(),
        vector_dimension: vectorizer.get_embedding_dimension(),
    })
}

/// Simple search helper usable from Tauri too
pub async fn search_db<P: AsRef<Path>>(
    book_dir: P,
    query: &str,
    limit: usize,
    dimension: usize,
    vectorizer: VectorizerConfig,
) -> Result<Vec<crate::database::SearchResult>> {
    let db_path = book_dir.as_ref().join("vectors.sqlite");
    let db = VectorDatabase::new(db_path, dimension).context("Open database failed")?;
    let v = TextVectorizer::new(vectorizer).await?;
    let embedding = v.vectorize_text(query).await?;
    db.search_similar(&embedding, limit)
}

fn sanitize_filename(name: &str) -> String {
    // Replace reserved characters but preserve Unicode; then safely limit length.
    let mut s: String = name
        .chars()
        .map(|c| match c {
            '/' | '\\' | ':' | '*' | '?' | '"' | '<' | '>' | '|' => '_',
            _ => c,
        })
        .collect();

    // Truncate by char boundary to avoid slicing inside a multi-byte codepoint.
    const MAX_LEN: usize = 80;
    if s.len() > MAX_LEN {
        // Fast path: try to truncate at MAX_LEN or backtrack to nearest char boundary
        let mut end = MAX_LEN;
        while end > 0 && !s.is_char_boundary(end) {
            end -= 1;
        }
        s.truncate(end);
    }

    if s.trim().is_empty() { "chapter".to_string() } else { s }
}

fn write_metadata_markdown(book_dir: &Path, epub_content: &EpubContent, flat_toc: &[FlatTocNode], toc_base_dir: &Path) -> Result<()> {
    // Try read metadata.json from book_dir
    let metadata_path = book_dir.join("metadata.json");
    let meta_file: Option<BookMetadataFile> = match fs::read_to_string(&metadata_path) {
        Ok(s) => match serde_json::from_str::<BookMetadataFile>(&s) {
            Ok(m) => Some(m),
            Err(e) => {
                log::warn!("metadata.json 解析失败：{} — 将使用 EPUB 信息兜底", e);
                None
            }
        },
        Err(_) => None,
    };

    // Merge fields with EPUB fallback
    let title = meta_file
        .as_ref()
        .and_then(|m| m.title.as_ref())
        .cloned()
        .unwrap_or_else(|| epub_content.title.clone());
    let author = meta_file
        .as_ref()
        .and_then(|m| m.author.as_ref())
        .map(|a| match a {
            AuthorField::Person(p) => p.name.clone().unwrap_or_default(),
            AuthorField::List(list) => {
                let names: Vec<String> = list.iter().filter_map(|p| p.name.clone()).collect();
                if names.is_empty() { String::new() } else { names.join("、") }
            }
            AuthorField::String(s) => s.clone(),
        })
        .filter(|s| !s.trim().is_empty())
        .unwrap_or_else(|| epub_content.author.clone());
    let language = meta_file.as_ref().and_then(|m| m.language.clone()).unwrap_or_else(|| "".to_string());
    let published = meta_file.as_ref().and_then(|m| m.published.clone()).unwrap_or_else(|| "".to_string());
    let publisher = meta_file.as_ref().and_then(|m| m.publisher.clone()).unwrap_or_else(|| "".to_string());

    let mut md = String::new();
    md.push_str(&format!("# {}\n\n", title));
    md.push_str("书籍元信息\n\n");
    md.push_str(&format!("- 标题: {}\n", title));
    md.push_str(&format!("- 作者: {}\n", author));
    if !publisher.is_empty() {
        md.push_str(&format!("- 出版社: {}\n", publisher));
    }
    if !published.is_empty() {
        md.push_str(&format!("- 出版日期: {}\n", published));
    }
    if !language.is_empty() {
        md.push_str(&format!("- 语言: {}\n", language));
    }
    md.push_str("\n");

    md.push_str("## 目录\n\n");
    md.push_str("说明：每项方括号内的是 TOC_ID（用于 ragToc 工具的 toc_id 参数）。\n\n");
    for node in flat_toc {
        // indent two spaces per depth
        let indent = "  ".repeat(node.depth as usize);
        // Include toc_id to help tools like ragToc locate chapters precisely
        md.push_str(&format!("{}- [{}] {}\n", indent, node.id, node.title));
    }

    let out_path = book_dir.join("metadata.md");
    fs::write(&out_path, md).with_context(|| format!("写入 metadata.md 失败: {:?}", out_path))?;
    
    // Update metadata.json with base_dir
    let updated_metadata = BookMetadataFile {
        title: Some(title),
        language: if language.is_empty() { None } else { Some(language) },
        published: if published.is_empty() { None } else { Some(published) },
        publisher: if publisher.is_empty() { None } else { Some(publisher) },
        author: meta_file.as_ref().and_then(|m| m.author.clone()).or_else(|| {
            if epub_content.author.is_empty() { None } else { Some(AuthorField::String(epub_content.author.clone())) }
        }),
        base_dir: Some(toc_base_dir.to_string_lossy().to_string()),
    };
    
    let metadata_json = serde_json::to_string_pretty(&updated_metadata)
        .context("Failed to serialize metadata")?;
    fs::write(&metadata_path, metadata_json)
        .with_context(|| format!("写入 metadata.json 失败: {:?}", metadata_path))?;
    
    log::info!("已更新 metadata.json，添加了 base_dir: {:?}", toc_base_dir);
    
    Ok(())
}
