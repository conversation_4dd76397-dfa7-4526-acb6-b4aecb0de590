use anyhow::{Context, Result};
use rusqlite::ffi::sqlite3_auto_extension;
use rusqlite::{params, Connection};
use serde::{Deserialize, Serialize};
use sqlite_vec::sqlite3_vec_init;
use std::path::Path;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentChunk {
    pub id: Option<i64>,
    pub book_title: String,
    pub book_author: String,
    pub chapter_title: String,
    pub chapter_order: usize,
    pub chunk_text: String,
    pub chunk_order: usize,
    pub embedding: Vec<f32>,
    
    // TOC导航字段
    pub md_src: String,              // MD文件路径 "text/part001.md"
    pub toc_depth: u32,              // TOC层级深度 0,1,2...
    pub toc_id: String,              // TOC节点ID "num_1"
    pub toc_index: usize,            // 在扁平化TOC数组中的索引位置
    
    // 分块位置字段
    pub chunk_index_in_toc: usize,   // 在当前TOC节点中的第几个分块
    pub total_chunks_in_toc: usize,  // 当前TOC节点的总分块数
    
    // 全局位置字段
    pub global_chunk_index: usize,   // 在整本书中的全局分块序号
}

#[derive(Debug, Clone)]
pub struct SearchResult {
    pub chunk: DocumentChunk,
    pub similarity: f32,
}

pub struct VectorDatabase {
    conn: Connection,
    embedding_dimension: usize,
}

impl VectorDatabase {
    /// 创建或打开向量数据库
    pub fn new<P: AsRef<Path>>(db_path: P, embedding_dimension: usize) -> Result<Self> {
        // Register sqlite-vec extension globally before opening the connection
        unsafe {
            sqlite3_auto_extension(Some(std::mem::transmute(sqlite3_vec_init as *const ())));
        }
        let conn = Connection::open(db_path).context("Failed to open database")?;

        let mut db = Self {
            conn,
            embedding_dimension,
        };

        db.initialize_database()?;
        Ok(db)
    }

    /// 初始化数据库表结构
    fn initialize_database(&mut self) -> Result<()> {
        // 检查表是否已存在
        let table_exists = self.conn.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='document_chunks'")
            .and_then(|mut stmt| {
                let rows: Result<Vec<String>, _> = stmt.query_map([], |row| {
                    Ok(row.get::<_, String>(0)?)
                }).and_then(|rows| rows.collect());
                Ok(!rows?.is_empty())
            })
            .unwrap_or(false);

        if !table_exists {
            log::info!("正在初始化数据库表结构...");
        }

        // 创建文档块表
        self.conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS document_chunks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                book_title TEXT NOT NULL,
                book_author TEXT NOT NULL,
                chapter_title TEXT NOT NULL,
                chapter_order INTEGER NOT NULL,
                chunk_text TEXT NOT NULL,
                chunk_order INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                -- TOC导航字段
                md_src TEXT NOT NULL DEFAULT '',
                toc_depth INTEGER NOT NULL DEFAULT 0,
                toc_id TEXT NOT NULL DEFAULT '',
                toc_index INTEGER NOT NULL DEFAULT 0,
                
                -- 分块位置字段
                chunk_index_in_toc INTEGER NOT NULL DEFAULT 0,
                total_chunks_in_toc INTEGER NOT NULL DEFAULT 0,
                
                -- 全局位置字段
                global_chunk_index INTEGER NOT NULL DEFAULT 0
            )
            "#,
            [],
        )?;

        // 创建标准向量表（BLOB 存储，作为回退/调试用途；首选 vec0）
        self.conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS document_embeddings (
                chunk_id INTEGER PRIMARY KEY,
                embedding BLOB NOT NULL,
                FOREIGN KEY (chunk_id) REFERENCES document_chunks (id)
            )
            "#,
            [],
        )?;

        // 创建索引以提高查询性能
        self.conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_book_title ON document_chunks (book_title)",
            [],
        )?;

        self.conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_chapter_order ON document_chunks (chapter_order)",
            [],
        )?;

        // TOC 相关索引，用于上下文检索
        self.conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_toc_index ON document_chunks (toc_index)",
            [],
        )?;

        self.conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_toc_chunk_position ON document_chunks (toc_index, chunk_index_in_toc)",
            [],
        )?;

        self.conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_global_chunk_index ON document_chunks (global_chunk_index)",
            [],
        )?;

        self.conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_toc_id ON document_chunks (toc_id)",
            [],
        )?;

        if !table_exists {
            log::info!("数据库表结构初始化完成");
        }
        Ok(())
    }

    /// 尝试初始化 sqlite-vec 虚拟表（如果扩展可用）
    pub fn initialize_vec_table(&mut self) -> Result<()> {
        log::info!("初始化 sqlite-vec 虚拟表（嵌入式）...");
        let create_vec_table = format!(
            "CREATE VIRTUAL TABLE IF NOT EXISTS document_vectors USING vec0(embedding FLOAT[{}])",
            self.embedding_dimension
        );
        match self.conn.execute(&create_vec_table, []) {
            Ok(_) => {
                log::info!("sqlite-vec 虚拟表创建成功");
                Ok(())
            }
            Err(e) => {
                log::warn!("创建 vec0 虚拟表失败，回退到标准表搜索: {}", e);
                Ok(())
            }
        }
    }

    // Removed insert_chunk (unused); keep batch insert only

    /// 批量插入文档块
    pub fn insert_chunks_batch(&mut self, chunks: &[DocumentChunk]) -> Result<Vec<i64>> {
        let mut chunk_ids = Vec::new();

        let tx = self.conn.transaction()?;

        for chunk in chunks {
            let _id = tx.execute(
                r#"
                INSERT INTO document_chunks 
                (book_title, book_author, chapter_title, chapter_order, chunk_text, chunk_order,
                 md_src, toc_depth, toc_id, toc_index, 
                 chunk_index_in_toc, total_chunks_in_toc, global_chunk_index)
                VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)
                "#,
                params![
                    chunk.book_title,
                    chunk.book_author,
                    chunk.chapter_title,
                    chunk.chapter_order,
                    chunk.chunk_text,
                    chunk.chunk_order,
                    chunk.md_src,
                    chunk.toc_depth,
                    chunk.toc_id,
                    chunk.toc_index,
                    chunk.chunk_index_in_toc,
                    chunk.total_chunks_in_toc,
                    chunk.global_chunk_index
                ],
            )?;

            let chunk_id = tx.last_insert_rowid();
            chunk_ids.push(chunk_id);

            // 插入嵌入向量（标准表，供回退）
            let embedding_bytes = Self::serialize_embedding_static(&chunk.embedding)?;
            tx.execute(
                "INSERT INTO document_embeddings (chunk_id, embedding) VALUES (?1, ?2)",
                params![chunk_id, embedding_bytes],
            )?;

            // 同步尝试写入 vec0 虚拟表（若已创建则成功；否则忽略错误，搜索会回退）
            let _ = tx.execute(
                "INSERT INTO document_vectors (rowid, embedding) VALUES (?1, ?2)",
                params![chunk_id, embedding_bytes],
            );
        }

        tx.commit()?;
        log::info!("成功插入 {} 个文档块", chunks.len());
        Ok(chunk_ids)
    }

    /// 使用余弦相似度搜索最相似的文档块
    pub fn search_similar(
        &self,
        query_embedding: &[f32],
        limit: usize,
    ) -> Result<Vec<SearchResult>> {
        // 首先尝试使用 vec0 进行搜索
        if let Ok(results) = self.search_with_vec0(query_embedding, limit) {
            return Ok(results);
        }

        // 如果 vec0 不可用，使用传统方法
        self.search_with_standard_table(query_embedding, limit)
    }

    /// 使用 sqlite-vec 进行向量搜索
    fn search_with_vec0(&self, query_embedding: &[f32], limit: usize) -> Result<Vec<SearchResult>> {
        let query_bytes = Self::serialize_embedding_static(query_embedding)?;

        let mut stmt = self.conn.prepare(
            r#"
            SELECT 
                c.id, c.book_title, c.book_author, c.chapter_title, 
                c.chapter_order, c.chunk_text, c.chunk_order,
                c.md_src, c.toc_depth, c.toc_id, c.toc_index,
                c.chunk_index_in_toc, c.total_chunks_in_toc, c.global_chunk_index,
                v.distance
            FROM document_vectors v
            JOIN document_chunks c ON v.rowid = c.id
            WHERE v.embedding MATCH ?1
            ORDER BY v.distance
            LIMIT ?2
            "#,
        )?;

        let results = stmt.query_map(params![query_bytes, limit], |row| {
            let distance_f64: f64 = row.get(14)?; // Changed from 7 to 14
            let distance: f32 = distance_f64 as f32;
            let similarity = 1.0 / (1.0 + distance);

            Ok(SearchResult {
                chunk: DocumentChunk {
                    id: Some(row.get(0)?),
                    book_title: row.get(1)?,
                    book_author: row.get(2)?,
                    chapter_title: row.get(3)?,
                    chapter_order: row.get(4)?,
                    chunk_text: row.get(5)?,
                    chunk_order: row.get(6)?,
                    embedding: Vec::new(),
                    md_src: row.get(7)?,
                    toc_depth: row.get(8)?,
                    toc_id: row.get(9)?,
                    toc_index: row.get(10)?,
                    chunk_index_in_toc: row.get(11)?,
                    total_chunks_in_toc: row.get(12)?,
                    global_chunk_index: row.get(13)?,
                },
                similarity,
            })
        })?;

        let mut search_results = Vec::new();
        for result in results {
            search_results.push(result?);
        }

        Ok(search_results)
    }

    /// 使用标准表进行向量搜索（计算余弦相似度）
    fn search_with_standard_table(
        &self,
        query_embedding: &[f32],
        limit: usize,
    ) -> Result<Vec<SearchResult>> {
        let mut stmt = self.conn.prepare(
            r#"
            SELECT 
                c.id, c.book_title, c.book_author, c.chapter_title,
                c.chapter_order, c.chunk_text, c.chunk_order,
                c.md_src, c.toc_depth, c.toc_id, c.toc_index,
                c.chunk_index_in_toc, c.total_chunks_in_toc, c.global_chunk_index,
                e.embedding
            FROM document_chunks c
            JOIN document_embeddings e ON c.id = e.chunk_id
            "#,
        )?;

        let mut results = Vec::new();

        let rows = stmt.query_map([], |row| {
            let embedding_bytes: Vec<u8> = row.get(14)?;
            let embedding = Self::deserialize_embedding_static(&embedding_bytes).map_err(|e| {
                rusqlite::Error::ToSqlConversionFailure(Box::new(std::io::Error::new(
                    std::io::ErrorKind::InvalidData,
                    e.to_string(),
                )))
            })?;

            let similarity = self.cosine_similarity(query_embedding, &embedding);

            Ok((
                SearchResult {
                    chunk: DocumentChunk {
                        id: Some(row.get(0)?),
                        book_title: row.get(1)?,
                        book_author: row.get(2)?,
                        chapter_title: row.get(3)?,
                        chapter_order: row.get(4)?,
                        chunk_text: row.get(5)?,
                        chunk_order: row.get(6)?,
                        embedding: Vec::new(),
                        md_src: row.get(7)?,
                        toc_depth: row.get(8)?,
                        toc_id: row.get(9)?,
                        toc_index: row.get(10)?,
                        chunk_index_in_toc: row.get(11)?,
                        total_chunks_in_toc: row.get(12)?,
                        global_chunk_index: row.get(13)?,
                    },
                    similarity,
                },
                similarity,
            ))
        })?;

        for row_result in rows {
            let (result, similarity) =
                row_result.map_err(|e| anyhow::anyhow!("Database error: {}", e))?;
            results.push((result, similarity));
        }

        // 按相似度降序排序
        results.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // 取前 limit 个结果
        results.truncate(limit);

        Ok(results.into_iter().map(|(result, _)| result).collect())
    }

    // === 上下文检索 API ===

    /// 根据分块 ID 获取带前后文的分块
    pub fn get_chunk_with_context(
        &self,
        chunk_id: i64,
        prev_count: usize,
        next_count: usize,
    ) -> Result<Vec<DocumentChunk>> {
        // 首先获取目标分块的信息
        let target_chunk = self.get_chunk_by_id(chunk_id)?;
        let global_index = target_chunk.global_chunk_index;
        
        // 根据全局索引获取前后文
        self.get_chunks_by_global_index_range(
            global_index.saturating_sub(prev_count),
            global_index + next_count + 1,
        )
    }

    /// 根据 TOC 节点 ID 获取该节点的所有分块
    pub fn get_toc_chunks(&self, toc_id: &str) -> Result<Vec<DocumentChunk>> {
        let mut stmt = self.conn.prepare(
            r#"
            SELECT 
                id, book_title, book_author, chapter_title,
                chapter_order, chunk_text, chunk_order,
                md_src, toc_depth, toc_id, toc_index,
                chunk_index_in_toc, total_chunks_in_toc, global_chunk_index
            FROM document_chunks
            WHERE toc_id = ?1
            ORDER BY chunk_index_in_toc ASC
            "#,
        )?;

        let chunks = stmt.query_map(params![toc_id], |row| {
            Ok(DocumentChunk {
                id: Some(row.get(0)?),
                book_title: row.get(1)?,
                book_author: row.get(2)?,
                chapter_title: row.get(3)?,
                chapter_order: row.get(4)?,
                chunk_text: row.get(5)?,
                chunk_order: row.get(6)?,
                embedding: Vec::new(),
                md_src: row.get(7)?,
                toc_depth: row.get(8)?,
                toc_id: row.get(9)?,
                toc_index: row.get(10)?,
                chunk_index_in_toc: row.get(11)?,
                total_chunks_in_toc: row.get(12)?,
                global_chunk_index: row.get(13)?,
            })
        })?;

        let mut results = Vec::new();
        for chunk in chunks {
            results.push(chunk.context("Failed to load chunk from database")?);
        }
        
        Ok(results)
    }

    /// 根据 TOC 索引范围获取相邻 TOC 节点的分块
    pub fn get_adjacent_toc_chunks(
        &self,
        center_toc_index: usize,
        prev_toc_count: usize,
        next_toc_count: usize,
    ) -> Result<Vec<DocumentChunk>> {
        let start_toc_index = center_toc_index.saturating_sub(prev_toc_count);
        let end_toc_index = center_toc_index + next_toc_count + 1;

        let mut stmt = self.conn.prepare(
            r#"
            SELECT 
                id, book_title, book_author, chapter_title,
                chapter_order, chunk_text, chunk_order,
                md_src, toc_depth, toc_id, toc_index,
                chunk_index_in_toc, total_chunks_in_toc, global_chunk_index
            FROM document_chunks
            WHERE toc_index >= ?1 AND toc_index < ?2
            ORDER BY toc_index ASC, chunk_index_in_toc ASC
            "#,
        )?;

        let chunks = stmt.query_map(params![start_toc_index, end_toc_index], |row| {
            Ok(DocumentChunk {
                id: Some(row.get(0)?),
                book_title: row.get(1)?,
                book_author: row.get(2)?,
                chapter_title: row.get(3)?,
                chapter_order: row.get(4)?,
                chunk_text: row.get(5)?,
                chunk_order: row.get(6)?,
                embedding: Vec::new(),
                md_src: row.get(7)?,
                toc_depth: row.get(8)?,
                toc_id: row.get(9)?,
                toc_index: row.get(10)?,
                chunk_index_in_toc: row.get(11)?,
                total_chunks_in_toc: row.get(12)?,
                global_chunk_index: row.get(13)?,
            })
        })?;

        let mut results = Vec::new();
        for chunk in chunks {
            results.push(chunk.context("Failed to load chunk from database")?);
        }
        
        Ok(results)
    }

    /// 根据全局分块索引范围获取分块
    pub fn get_chunks_by_global_index_range(&self, start_index: usize, end_index: usize) -> Result<Vec<DocumentChunk>> {
        let mut stmt = self.conn.prepare(
            r#"
            SELECT 
                id, book_title, book_author, chapter_title,
                chapter_order, chunk_text, chunk_order,
                md_src, toc_depth, toc_id, toc_index,
                chunk_index_in_toc, total_chunks_in_toc, global_chunk_index
            FROM document_chunks
            WHERE global_chunk_index >= ?1 AND global_chunk_index < ?2
            ORDER BY global_chunk_index ASC
            "#,
        )?;

        let chunks = stmt.query_map(params![start_index, end_index], |row| {
            Ok(DocumentChunk {
                id: Some(row.get(0)?),
                book_title: row.get(1)?,
                book_author: row.get(2)?,
                chapter_title: row.get(3)?,
                chapter_order: row.get(4)?,
                chunk_text: row.get(5)?,
                chunk_order: row.get(6)?,
                embedding: Vec::new(),
                md_src: row.get(7)?,
                toc_depth: row.get(8)?,
                toc_id: row.get(9)?,
                toc_index: row.get(10)?,
                chunk_index_in_toc: row.get(11)?,
                total_chunks_in_toc: row.get(12)?,
                global_chunk_index: row.get(13)?,
            })
        })?;

        let mut results = Vec::new();
        for chunk in chunks {
            results.push(chunk.context("Failed to load chunk from database")?);
        }
        
        Ok(results)
    }

    /// 根据分块 ID 获取单个分块
    fn get_chunk_by_id(&self, chunk_id: i64) -> Result<DocumentChunk> {
        let mut stmt = self.conn.prepare(
            r#"
            SELECT 
                id, book_title, book_author, chapter_title,
                chapter_order, chunk_text, chunk_order,
                md_src, toc_depth, toc_id, toc_index,
                chunk_index_in_toc, total_chunks_in_toc, global_chunk_index
            FROM document_chunks
            WHERE id = ?1
            "#,
        )?;

        let chunk = stmt.query_row(params![chunk_id], |row| {
            Ok(DocumentChunk {
                id: Some(row.get(0)?),
                book_title: row.get(1)?,
                book_author: row.get(2)?,
                chapter_title: row.get(3)?,
                chapter_order: row.get(4)?,
                chunk_text: row.get(5)?,
                chunk_order: row.get(6)?,
                embedding: Vec::new(),
                md_src: row.get(7)?,
                toc_depth: row.get(8)?,
                toc_id: row.get(9)?,
                toc_index: row.get(10)?,
                chunk_index_in_toc: row.get(11)?,
                total_chunks_in_toc: row.get(12)?,
                global_chunk_index: row.get(13)?,
            })
        })?;

        Ok(chunk)
    }

    /// 计算余弦相似度
    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            dot_product / (norm_a * norm_b)
        }
    }

    /// 序列化嵌入向量为二进制格式
    fn serialize_embedding_static(embedding: &[f32]) -> Result<Vec<u8>> {
        let mut bytes = Vec::with_capacity(embedding.len() * 4);
        for &value in embedding {
            bytes.extend_from_slice(&value.to_le_bytes());
        }
        Ok(bytes)
    }

    /// 反序列化嵌入向量
    fn deserialize_embedding_static(bytes: &[u8]) -> Result<Vec<f32>> {
        if bytes.len() % 4 != 0 {
            anyhow::bail!("Invalid embedding data length");
        }

        let mut embedding = Vec::with_capacity(bytes.len() / 4);
        for chunk in bytes.chunks_exact(4) {
            let value = f32::from_le_bytes([chunk[0], chunk[1], chunk[2], chunk[3]]);
            embedding.push(value);
        }

        Ok(embedding)
    }
}
