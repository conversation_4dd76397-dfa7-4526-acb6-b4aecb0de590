use serde::Serialize;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager, Runtime, State};

use crate::database::{DocumentChunk, VectorDatabase};
use crate::epub_reader::EpubReader;
use crate::pipeline::{process_epub_to_db, ProcessOptions, ProcessReport, ProgressUpdate};
use crate::state::EpubState;
use crate::vectorizer::{TextVectorizer, VectorizerConfig};
use crate::parse_toc::{parse_toc_file, find_toc_ncx_in_mdbook, TocNode, FlatTocNode, flatten_toc};
use epub2mdbook::convert_epub_to_mdbook;

#[derive(Serialize)]
pub struct ParsedBook {
    pub title: String,
    pub author: String,
    pub chapters: usize,
}

#[derive(Serialize)]
pub struct IndexResult {
    pub success: bool,
    pub message: String,
    pub report: Option<ProcessReportDto>,
}

#[derive(Serialize)]
pub struct MdbookResult {
    pub success: bool,
    pub message: String,
    #[serde(rename = "outputDir")]
    pub output_dir: Option<String>,
}

#[derive(Serialize)]
pub struct ProcessReportDto {
    pub db_path: String,
    pub book_title: String,
    pub book_author: String,
    pub total_chunks: usize,
    pub vector_dimension: usize,
}

impl From<ProcessReport> for ProcessReportDto {
    fn from(r: ProcessReport) -> Self {
        Self {
            db_path: r.db_path.to_string_lossy().to_string(),
            book_title: r.book_title,
            book_author: r.book_author,
            total_chunks: r.total_chunks,
            vector_dimension: r.vector_dimension,
        }
    }
}

/// Parse an EPUB under $AppData/books/{book_id} and return basic metadata.
#[tauri::command]
pub async fn parse_epub<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
) -> Result<ParsedBook, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let epub_path = book_dir.join("book.epub");
    let reader = EpubReader::new().map_err(|e| e.to_string())?;
    let content = reader.read_epub(&epub_path).map_err(|e| e.to_string())?;
    Ok(ParsedBook {
        title: content.title,
        author: content.author,
        chapters: content.chapters.len(),
    })
}

/// Index an EPUB: resolve book_dir from $AppData/books/{book_id},
/// parse, write chapters txt, vectorize and persist locally.
#[tauri::command]
pub async fn index_epub<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    dimension: Option<usize>,
    base_url: String,
    model: String,
    api_key: Option<String>,
) -> Result<IndexResult, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);

    #[derive(Serialize, Clone)]
    struct IndexProgressEvent {
        book_id: String,
        current: usize,
        total: usize,
        percent: f32,
        chapter_title: String,
        chunk_index: usize,
    }

    let app_for_emit = app.clone();
    let book_id_for_emit = book_id.clone();

    let report = process_epub_to_db(
        &book_dir,
        ProcessOptions {
            dimension: dimension.unwrap_or(1024),
            batch_size: None,
            vectorizer: VectorizerConfig {
                base_url,
                model_name: model,
                api_key,
            },
        },
        Some(move |u: ProgressUpdate| {
            let payload = IndexProgressEvent {
                book_id: book_id_for_emit.clone(),
                current: u.current,
                total: u.total,
                percent: u.percent,
                chapter_title: u.chapter_title,
                chunk_index: u.chunk_index,
            };
            let _ = app_for_emit.emit("epub://index-progress", payload);
        }),
    )
    .await
    .map_err(|e| e.to_string())?;

    Ok(IndexResult {
        success: true,
        message: "indexed".into(),
        report: Some(report.into()),
    })
}

/// Convert an EPUB under $AppData/books/{book_id} to mdBook structure at {book_dir}/mdbook
#[tauri::command]
pub async fn convert_to_mdbook<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    overwrite: Option<bool>,
) -> Result<MdbookResult, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }

    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let epub_path = book_dir.join("book.epub");
    let mdbook_dir = book_dir.join("mdbook");

    if !epub_path.exists() {
        return Err(format!("EPUB not found: {}", epub_path.to_string_lossy()));
    }
    if !mdbook_dir.exists() {
        std::fs::create_dir_all(&mdbook_dir).map_err(|e| e.to_string())?;
    }

    let ow = overwrite.unwrap_or(true);
    log::info!(
        "convert_to_mdbook: book_id={}, epub_path={:?}, output_dir={:?}, overwrite={}",
        book_id,
        epub_path,
        mdbook_dir,
        ow
    );
    match convert_epub_to_mdbook(&epub_path, &mdbook_dir, ow) {
        Ok(_) => {
            log::info!("convert_to_mdbook: success at {:?}", mdbook_dir);
            Ok(MdbookResult {
                success: true,
                message: "converted".into(),
                output_dir: Some(mdbook_dir.to_string_lossy().to_string()),
            })
        }
        Err(e) => {
            log::error!("convert_to_mdbook: failed: {}", e);
            Err(format!("convert epub->mdbook failed: {}", e))
        }
    }
}

/// Parse the TOC structure of an EPUB book, returning a flattened array
#[tauri::command]
pub async fn parse_toc<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
) -> Result<Vec<FlatTocNode>, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }

    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let mdbook_dir = book_dir.join("mdbook");

    // 在 mdbook 目录下递归搜索 toc.ncx
    let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)
        .ok_or_else(|| "TOC file (toc.ncx) not found in MDBook directory".to_string())?;

    let toc_nodes = parse_toc_file(&toc_path)?;
    let flat_toc = flatten_toc(&toc_nodes);
    Ok(flat_toc)
}

#[derive(Serialize)]
pub struct SearchItemDto {
    pub book_title: String,
    pub book_author: String,
    pub chapter_title: String,
    pub content: String,
    pub similarity: f32,
    
    // 新增位置字段，用于AI智能上下文检索
    pub chunk_id: Option<i64>,
    pub toc_id: String,
    pub toc_depth: u32,
    pub global_chunk_index: usize,
    pub chunk_index_in_toc: usize,
    pub total_chunks_in_toc: usize,
    pub md_src: String,
}

/// Search the vector database for similar chunks.
#[tauri::command]
pub async fn search_db<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    query: String,
    limit: Option<usize>,
    dimension: Option<usize>,
    base_url: String,
    model: String,
    api_key: Option<String>,
) -> Result<Vec<SearchItemDto>, String> {
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(book_id);
    let results = crate::pipeline::search_db(
        &book_dir,
        &query,
        limit.unwrap_or(5),
        dimension.unwrap_or(1024),
        VectorizerConfig {
            base_url,
            model_name: model,
            api_key,
        },
    )
    .await
    .map_err(|e| e.to_string())?;

    Ok(results
        .into_iter()
        .map(|r| SearchItemDto {
            book_title: r.chunk.book_title,
            book_author: r.chunk.book_author,
            chapter_title: r.chunk.chapter_title,
            content: r.chunk.chunk_text,
            similarity: r.similarity,
            chunk_id: r.chunk.id,
            toc_id: r.chunk.toc_id,
            toc_depth: r.chunk.toc_depth,
            global_chunk_index: r.chunk.global_chunk_index,
            chunk_index_in_toc: r.chunk.chunk_index_in_toc,
            total_chunks_in_toc: r.chunk.total_chunks_in_toc,
            md_src: r.chunk.md_src,
        })
        .collect())
}

/// Get chunk with context by chunk ID
#[tauri::command]
pub async fn get_chunk_with_context<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    chunk_id: i64,
    prev_count: usize,
    next_count: usize,
) -> Result<Vec<DocumentChunkDto>, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let db_path = book_dir.join("vectors.sqlite");
    
    let db = VectorDatabase::new(&db_path, 1024).map_err(|e| e.to_string())?;
    let chunks = db.get_chunk_with_context(chunk_id, prev_count, next_count)
        .map_err(|e| e.to_string())?;
    
    Ok(chunks.into_iter().map(DocumentChunkDto::from).collect())
}

/// Get all chunks for a TOC node
#[tauri::command]
pub async fn get_toc_chunks<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    toc_id: String,
) -> Result<Vec<DocumentChunkDto>, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let db_path = book_dir.join("vectors.sqlite");
    
    let db = VectorDatabase::new(&db_path, 1024).map_err(|e| e.to_string())?;
    let chunks = db.get_toc_chunks(&toc_id).map_err(|e| e.to_string())?;
    
    Ok(chunks.into_iter().map(DocumentChunkDto::from).collect())
}

/// Get chunks by global index range
#[tauri::command]
pub async fn get_chunks_by_range<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    start_index: usize,
    end_index: usize,
) -> Result<Vec<DocumentChunkDto>, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let db_path = book_dir.join("vectors.sqlite");
    
    let db = VectorDatabase::new(&db_path, 1024).map_err(|e| e.to_string())?;
    let chunks = db.get_chunks_by_global_index_range(start_index, end_index)
        .map_err(|e| e.to_string())?;
    
    Ok(chunks.into_iter().map(DocumentChunkDto::from).collect())
}

#[derive(Serialize)]
pub struct DocumentChunkDto {
    pub id: Option<i64>,
    pub book_title: String,
    pub book_author: String,
    pub chapter_title: String,
    pub chapter_order: usize,
    pub chunk_text: String,
    pub chunk_order: usize,
    pub md_src: String,
    pub toc_depth: u32,
    pub toc_id: String,
    pub toc_index: usize,
    pub chunk_index_in_toc: usize,
    pub total_chunks_in_toc: usize,
    pub global_chunk_index: usize,
}

impl From<DocumentChunk> for DocumentChunkDto {
    fn from(chunk: DocumentChunk) -> Self {
        Self {
            id: chunk.id,
            book_title: chunk.book_title,
            book_author: chunk.book_author,
            chapter_title: chunk.chapter_title,
            chapter_order: chunk.chapter_order,
            chunk_text: chunk.chunk_text,
            chunk_order: chunk.chunk_order,
            md_src: chunk.md_src,
            toc_depth: chunk.toc_depth,
            toc_id: chunk.toc_id,
            toc_index: chunk.toc_index,
            chunk_index_in_toc: chunk.chunk_index_in_toc,
            total_chunks_in_toc: chunk.total_chunks_in_toc,
            global_chunk_index: chunk.global_chunk_index,
        }
    }
}
