use roxmltree::{Document, Node};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs;
use regex::Regex;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TocNode {
    pub id: String,
    pub play_order: u32,
    pub title: String,
    pub src: String,
    pub children: Vec<TocNode>,
}

/// 扁平化后的TOC节点，包含深度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlatTocNode {
    pub id: String,
    pub play_order: u32,
    pub title: String,
    pub md_src: String,  // 转换后的 MD 文件路径（不包含锚点）
    pub depth: u32,      // 节点深度，从0开始
    #[serde(default)]
    pub anchor: Option<String>, // 原始 src 的锚点（如果有）
}

/// 解析 toc.ncx 文件，返回结构化的目录数据
pub fn parse_toc_file<P: AsRef<Path>>(toc_path: P) -> Result<Vec<TocNode>, String> {
    let toc_content = std::fs::read_to_string(toc_path).map_err(|e| e.to_string())?;
    parse_toc_content(&toc_content)
}

/// 解析 NCX 文件内容
pub fn parse_toc_content(content: &str) -> Result<Vec<TocNode>, String> {
    // roxmltree 不支持 DTD，某些 toc.ncx 带有 <!DOCTYPE ...>，需要在解析前移除
    let sanitized = strip_doctype(content);
    let doc = Document::parse(&sanitized).map_err(|e| format!("Failed to parse XML: {}", e))?;
    
    // 查找 navMap 元素
    let nav_map = doc
        .root_element()
        .children()
        .find(|node| node.tag_name().name() == "navMap")
        .ok_or("navMap element not found")?;
    
    // 解析所有顶层的 navPoint
    let nav_points: Vec<TocNode> = nav_map
        .children()
        .filter(|node| node.is_element() && node.tag_name().name() == "navPoint")
        .map(parse_nav_point)
        .collect::<Result<Vec<_>, _>>()?;
    
    Ok(nav_points)
}

/// 去除 XML 中的 DOCTYPE 声明（包含可能的内部子集），以便 roxmltree 能解析
fn strip_doctype(input: &str) -> String {
    // 简单且鲁棒：跨行匹配，移除从 <!DOCTYPE 到下一个 >
    // 该模式覆盖大部分 toc.ncx 的 DTD 写法（含内部子集 ]>）
    let re = Regex::new(r"(?is)<!DOCTYPE.*?>").unwrap();
    re.replace(input, "").to_string()
}

/// 递归解析 navPoint 节点
fn parse_nav_point(node: Node) -> Result<TocNode, String> {
    // 获取属性
    let id = node
        .attribute("id")
        .ok_or("navPoint missing id attribute")?
        .to_string();
    
    let play_order: u32 = node
        .attribute("playOrder")
        .ok_or("navPoint missing playOrder attribute")?
        .parse()
        .map_err(|_| "Invalid playOrder value")?;
    
    // 获取 navLabel 中的 text
    let title = node
        .children()
        .find(|child| child.tag_name().name() == "navLabel")
        .ok_or("navLabel not found")?
        .children()
        .find(|child| child.tag_name().name() == "text")
        .ok_or("text node not found in navLabel")?
        .text()
        .ok_or("text content not found")?
        .to_string();
    
    // 获取 content 的 src 属性
    let src = node
        .children()
        .find(|child| child.tag_name().name() == "content")
        .ok_or("content not found")?
        .attribute("src")
        .ok_or("src attribute not found")?
        .to_string();
    
    // 递归解析子节点
    let children: Vec<TocNode> = node
        .children()
        .filter(|child| child.is_element() && child.tag_name().name() == "navPoint")
        .map(parse_nav_point)
        .collect::<Result<Vec<_>, _>>()?;
    
    Ok(TocNode {
        id,
        play_order,
        title,
        src,
        children,
    })
}

/// 根据 mdbook 目录结构查找 TOC 文件
pub fn resolve_mdbook_toc<P: AsRef<Path>>(book_dir: P) -> Option<std::path::PathBuf> {
    let base_dir = book_dir.as_ref();
    let mdbook_src = base_dir.join("mdbook").join("book").join("src");
    
    // 优先查找 toc.ncx
    let toc_ncx = mdbook_src.join("toc.ncx");
    if toc_ncx.exists() {
        return Some(toc_ncx);
    }
    
    // 备选查找 toc.xhtml
    let toc_xhtml = mdbook_src.join("toc.xhtml");
    if toc_xhtml.exists() {
        return Some(toc_xhtml);
    }
    
    None
}

/// 提取文件名部分（不包含锚点）
pub fn extract_filename_from_src(src: &str) -> String {
    src.split('#').next().unwrap_or(src).to_string()
}

/// 将 HTML/XHTML 等文件名转换为对应的 Markdown 文件名
pub fn html_to_md_filename(html_filename: &str) -> String {
    // 支持多种Web文档格式的扩展名
    let extensions_to_replace = [
        ".html",
        ".xhtml", 
        ".htm",
        ".xml",
        ".xht",
    ];
    
    // 查找匹配的扩展名并替换
    for ext in &extensions_to_replace {
        if html_filename.ends_with(ext) {
            let base_name = &html_filename[..html_filename.len() - ext.len()];
            return format!("{}.md", base_name);
        }
    }
    
    // 如果没有匹配的扩展名，直接添加.md
    format!("{}.md", html_filename)
}

/// 将嵌套的TOC结构扁平化为数组
pub fn flatten_toc(toc_nodes: &[TocNode]) -> Vec<FlatTocNode> {
    let mut result = Vec::new();
    for node in toc_nodes {
        flatten_toc_recursive(node, 0, &mut result);
    }
    result
}

/// 递归扁平化TOC节点
fn flatten_toc_recursive(node: &TocNode, depth: u32, result: &mut Vec<FlatTocNode>) {
    // 提取文件名并转换为MD格式
    let filename = extract_filename_from_src(&node.src);
    let md_src = html_to_md_src(&node.src);
    let anchor = extract_anchor_from_src(&node.src);
    
    // 添加当前节点到结果中
    result.push(FlatTocNode {
        id: node.id.clone(),
        play_order: node.play_order,
        title: node.title.clone(),
        md_src,
        depth,
        anchor,
    });
    
    // 递归处理子节点
    for child in &node.children {
        flatten_toc_recursive(child, depth + 1, result);
    }
}

/// 将HTML源路径转换为MD源路径
pub fn html_to_md_src(src: &str) -> String {
    // 移除锚点部分，只保留文件路径
    let file_part = if let Some(anchor_pos) = src.find('#') {
        &src[..anchor_pos]
    } else {
        src
    };
    
    // URL解码，处理特殊字符如 %2c -> ,
    let decoded_path = url_decode(file_part);
    html_to_md_filename(&decoded_path)
}

/// 从 src 提取锚点（#之后的部分），无则返回 None
pub fn extract_anchor_from_src(src: &str) -> Option<String> {
    src.split('#').nth(1).map(|s| s.to_string()).filter(|s| !s.is_empty())
}

/// 简单的URL解码函数，处理常见的URL编码字符
fn url_decode(input: &str) -> String {
    // 使用标准的百分号解码并按 UTF-8 解析；
    // 对于无效序列使用无损策略保留原字符。
    percent_encoding::percent_decode_str(input)
        .decode_utf8_lossy()
        .into_owned()
}

/// 在指定目录下递归搜索 toc.ncx 文件
pub fn find_toc_ncx_in_mdbook<P: AsRef<Path>>(mdbook_dir: P) -> Option<PathBuf> {
    fn search_recursive(dir: &Path) -> Option<PathBuf> {
        // 读取目录内容
        let entries = match fs::read_dir(dir) {
            Ok(entries) => entries,
            Err(_) => return None,
        };

        for entry in entries {
            let entry = match entry {
                Ok(entry) => entry,
                Err(_) => continue,
            };
            
            let path = entry.path();
            
            if path.is_file() {
                // 检查是否是 toc.ncx 文件
                if let Some(filename) = path.file_name() {
                    if filename == "toc.ncx" {
                        return Some(path);
                    }
                }
            } else if path.is_dir() {
                // 递归搜索子目录
                if let Some(found) = search_recursive(&path) {
                    return Some(found);
                }
            }
        }
        None
    }
    
    let mdbook_path = mdbook_dir.as_ref();
    if mdbook_path.exists() && mdbook_path.is_dir() {
        search_recursive(mdbook_path)
    } else {
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_html_to_md_src() {
        // 基本测试 - .html
        assert_eq!(html_to_md_src("chapter1.html"), "chapter1.md");
        assert_eq!(html_to_md_src("chapter1.html#section1"), "chapter1.md");
        
        // 测试不同扩展名
        assert_eq!(html_to_md_src("chapter2.xhtml"), "chapter2.md");
        assert_eq!(html_to_md_src("chapter3.htm"), "chapter3.md");
        assert_eq!(html_to_md_src("toc.xml"), "toc.md");
        assert_eq!(html_to_md_src("page.xht"), "page.md");
        
        // URL编码测试 - 特别测试逗号的情况
        assert_eq!(html_to_md_src("Si_Kao_%2cKuai_Yu_Man_split_121.html"), "Si_Kao_,Kuai_Yu_Man_split_121.md");
        assert_eq!(html_to_md_src("Si_Kao_%2cKuai_Yu_Man_split_121.xhtml"), "Si_Kao_,Kuai_Yu_Man_split_121.md");
        assert_eq!(html_to_md_src("file%20with%20spaces.xhtml"), "file with spaces.md");
        assert_eq!(html_to_md_src("special%21chars%40test.htm"), "special!<EMAIL>");
        
        // 带锚点的URL编码测试
        assert_eq!(html_to_md_src("Si_Kao_%2cKuai_Yu_Man_split_121.xhtml#anchor"), "Si_Kao_,Kuai_Yu_Man_split_121.md");
        
        // 无扩展名或未知扩展名
        assert_eq!(html_to_md_src("chapter_no_ext"), "chapter_no_ext.md");
        assert_eq!(html_to_md_src("chapter.txt"), "chapter.txt.md");
    }

    #[test]
    fn test_html_to_md_filename() {
        // 测试各种支持的扩展名
        assert_eq!(html_to_md_filename("chapter.html"), "chapter.md");
        assert_eq!(html_to_md_filename("chapter.xhtml"), "chapter.md");
        assert_eq!(html_to_md_filename("chapter.htm"), "chapter.md");
        assert_eq!(html_to_md_filename("toc.xml"), "toc.md");
        assert_eq!(html_to_md_filename("page.xht"), "page.md");
        
        // 测试复杂文件名
        assert_eq!(html_to_md_filename("chapter_01_introduction.xhtml"), "chapter_01_introduction.md");
        assert_eq!(html_to_md_filename("Si_Kao_,Kuai_Yu_Man_split_121.html"), "Si_Kao_,Kuai_Yu_Man_split_121.md");
        
        // 测试无扩展名或未知扩展名
        assert_eq!(html_to_md_filename("chapter"), "chapter.md");
        assert_eq!(html_to_md_filename("chapter.txt"), "chapter.txt.md");
        assert_eq!(html_to_md_filename("chapter.pdf"), "chapter.pdf.md");
    }

    #[test]
    fn test_url_decode() {
        // 测试逗号解码
        assert_eq!(url_decode("Si_Kao_%2cKuai_Yu_Man_split_121"), "Si_Kao_,Kuai_Yu_Man_split_121");
        // 测试空格解码
        assert_eq!(url_decode("file%20with%20spaces"), "file with spaces");
        // 测试多种字符解码
        assert_eq!(url_decode("test%21%40%23%24"), "test!@#$");
        // 测试无需解码的字符串
        assert_eq!(url_decode("normal_filename"), "normal_filename");
        // 测试不完整的编码
        assert_eq!(url_decode("test%2"), "test%2");
        assert_eq!(url_decode("test%"), "test%");
    }

    #[test] 
    fn test_find_toc_ncx_in_mdbook() {
        use std::fs;
        use tempfile::tempdir;
        
        // 创建临时目录结构模拟mdbook的src目录
        let temp_dir = tempdir().expect("Failed to create temp dir");
        let mdbook_src = temp_dir.path().join("mdbook").join("book").join("src");
        fs::create_dir_all(&mdbook_src).expect("Failed to create directories");
        
        // 创建 toc.ncx 文件
        let toc_ncx_path = mdbook_src.join("toc.ncx");
        fs::write(&toc_ncx_path, "").expect("Failed to write toc.ncx");
        
        // 测试能找到 toc.ncx
        let found_toc_ncx = find_toc_ncx_in_mdbook(&temp_dir.path());
        assert!(found_toc_ncx.is_some());
        assert_eq!(found_toc_ncx.unwrap(), toc_ncx_path);
        
        // 测试找不到的文件
        let not_found = find_toc_ncx_in_mdbook(&temp_dir.path().join("nonexistent_dir"));
        assert!(not_found.is_none());
    }
}
