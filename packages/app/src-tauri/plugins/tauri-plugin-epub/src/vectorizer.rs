use anyhow::{Context, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use tiktoken_rs::o200k_base;

#[derive(Serialize)]
struct EmbeddingRequest {
    input: Vec<String>,
    model: String,
    encoding_format: String,
}

#[derive(Deserialize)]
struct EmbeddingResponse {
    data: Vec<EmbeddingData>,
}

#[derive(Deserialize)]
struct EmbeddingData {
    embedding: Vec<f32>,
}

// Removed Usage: only `data` is required

pub struct TextVectorizer {
    client: Client,
    api_key: Option<String>,
    model_name: String,
    base_url: String,
    tokenizer: tiktoken_rs::CoreBPE,
}

#[derive(Clone, Debug)]
pub struct VectorizerConfig {
    pub base_url: String,
    pub model_name: String,
    pub api_key: Option<String>,
}

impl TextVectorizer {
    /// 创建新的文本向量化器
    pub async fn new(config: VectorizerConfig) -> Result<Self> {
        log::info!("初始化嵌入 API 向量化器: base_url={}, model={}", config.base_url, config.model_name);

        let client = Client::new();
        let tokenizer = o200k_base().context("Failed to initialize tiktoken tokenizer")?;

        Ok(Self {
            client,
            api_key: config.api_key,
            model_name: config.model_name,
            base_url: config.base_url,
            tokenizer,
        })
    }

    /// 将文本转换为向量
    pub async fn vectorize_text(&self, text: &str) -> Result<Vec<f32>> {
        // 按 token 数量截断，避免超过后端上下文窗口
        // 预留安全边界，假设后端窗口至少 512（llama.cpp 默认可调），这里取 480 tokens
        let max_tokens: usize = 480;
        let tokens = self.tokenizer.encode_with_special_tokens(text);
        let processed_text = if tokens.len() > max_tokens {
            log::warn!(
                "文本过长 ({} tokens)，按 token 截断到 {} tokens",
                tokens.len(),
                max_tokens
            );
            let preview = text.chars().take(120).collect::<String>();
            log::debug!("原文本预览(120)：{}", preview);
            let clipped = &tokens[..max_tokens];
            // 将截断后的 token 反解码为字符串
            self.tokenizer.decode(clipped.to_vec())
                .unwrap_or_else(|_| text.chars().take(1000).collect::<String>())
        } else {
            text.to_string()
        };

        let request = EmbeddingRequest {
            input: vec![processed_text],
            model: self.model_name.clone(),
            encoding_format: "float".to_string(),
        };

        let url = format!("{}/v1/embeddings", self.base_url);
        let mut req = self.client.post(&url).header("Content-Type", "application/json").json(&request);
        if let Some(k) = &self.api_key { req = req.header("Authorization", format!("Bearer {}", k)); }
        let response = req
            .send()
            .await
            .context("Failed to send request to local embedding API")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Local embedding API error: {}", error_text);
        }

        let embedding_response: EmbeddingResponse = response
            .json()
            .await
            .context("Failed to parse local embedding API response")?;

        if embedding_response.data.is_empty() {
            anyhow::bail!("No embeddings returned from local embedding API");
        }

        Ok(embedding_response.data[0].embedding.clone())
    }

    /// 获取嵌入向量的维度
    pub fn get_embedding_dimension(&self) -> usize {
        // jina-embeddings-v3 的嵌入维度是 1024
        1024
    }
}
