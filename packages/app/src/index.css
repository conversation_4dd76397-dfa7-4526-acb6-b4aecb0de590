@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";
@custom-variant dark (&:is(.dark *));

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.35);
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

:root {
  --radius: .625rem;
  --background: oklch(100% 0 0);
  --foreground: oklch(14.5% 0 0);
  --card: oklch(100% 0 0);
  --card-foreground: oklch(14.5% 0 0);
  --popover: oklch(100% 0 0);
  --popover-foreground: oklch(14.5% 0 0);
  --primary: oklch(20.5% 0 0);
  --primary-foreground: oklch(98.5% 0 0);
  --secondary: oklch(97% 0 0);
  --secondary-foreground: oklch(20.5% 0 0);
  --muted: oklch(97% 0 0);
  --muted-foreground: oklch(55.6% 0 0);
  --accent: oklch(97% 0 0);
  --accent-foreground: oklch(20.5% 0 0);
  --destructive: oklch(57.7% 0.245 27.325);
  --border: oklch(92.2% 0 0);
  --input: oklch(92.2% 0 0);
  --ring: oklch(70.8% 0 0);
  --chart-1: var(--color-blue-300);
  --chart-2: var(--color-blue-500);
  --chart-3: var(--color-blue-600);
  --chart-4: var(--color-blue-700);
  --chart-5: var(--color-blue-800);
  --sidebar: oklch(98.5% 0 0);
  --sidebar-foreground: oklch(14.5% 0 0);
  --sidebar-primary: oklch(20.5% 0 0);
  --sidebar-primary-foreground: oklch(98.5% 0 0);
  --sidebar-accent: oklch(97% 0 0);
  --sidebar-accent-foreground: oklch(20.5% 0 0);
  --sidebar-border: oklch(92.2% 0 0);
  --sidebar-ring: oklch(70.8% 0 0);
  --surface: oklch(98% 0 0);
  --surface-foreground: var(--foreground);
  --code: var(--surface);
  --code-foreground: var(--surface-foreground);
  --code-highlight: oklch(96% 0 0);
  --code-number: oklch(56% 0 0);
  --selection: oklch(14.5% 0 0);
  --selection-foreground: oklch(100% 0 0);
  --background-active: #dee1e6;
  --app-background: #eaebee;
  --tab-background: oklch(91.8% 0.004 247.858);
}

.dark {
  --background: oklch(14.5% 0 0);
  --foreground: oklch(98.5% 0 0);
  --card: oklch(20.5% 0 0);
  --card-foreground: oklch(98.5% 0 0);
  --popover: oklch(26.9% 0 0);
  --popover-foreground: oklch(98.5% 0 0);
  --primary: oklch(92.2% 0 0);
  --primary-foreground: oklch(20.5% 0 0);
  --secondary: oklch(26.9% 0 0);
  --secondary-foreground: oklch(98.5% 0 0);
  --muted: oklch(26.9% 0 0);
  --muted-foreground: oklch(70.8% 0 0);
  --accent: oklch(37.1% 0 0);
  --accent-foreground: oklch(98.5% 0 0);
  --destructive: oklch(70.4% 0.191 22.216);
  --border: oklch(100% 0 0 / 0.1);
  --input: oklch(100% 0 0 / 0.15);
  --ring: oklch(55.6% 0 0);
  --chart-1: var(--color-blue-300);
  --chart-2: var(--color-blue-500);
  --chart-3: var(--color-blue-600);
  --chart-4: var(--color-blue-700);
  --chart-5: var(--color-blue-800);
  --sidebar: oklch(20.5% 0 0);
  --sidebar-foreground: oklch(98.5% 0 0);
  --sidebar-primary: oklch(48.8% 0.243 264.376);
  --sidebar-primary-foreground: oklch(98.5% 0 0);
  --sidebar-accent: oklch(26.9% 0 0);
  --sidebar-accent-foreground: oklch(98.5% 0 0);
  --sidebar-border: oklch(100% 0 0 / 0.1);
  --sidebar-ring: oklch(43.9% 0 0);
  --surface: oklch(20% 0 0);
  --surface-foreground: oklch(70.8% 0 0);
  --code: var(--surface);
  --code-foreground: var(--surface-foreground);
  --code-highlight: oklch(27% 0 0);
  --code-number: oklch(72% 0 0);
  --selection: oklch(92.2% 0 0);
  --selection-foreground: oklch(20.5% 0 0);
  --app-background: oklch(18.5% 0.004 247.858);
  --tab-background: #202124;
}

@theme {
  --breakpoint-lxg: 1120px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-app-background: var(--app-background);
  --color-tab-background: var(--tab-background);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

foliate-view {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }

  .custom-resize-handle {
    width: 8px;
    height: calc(100% - 4px);
    cursor: col-resize;
    position: absolute;
    left: 6px;
    top: 0;
    background: transparent;
    z-index: 10;
    padding: 0 2px;
  }

  .custom-resize-handle::after {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background-color: var(--color-blue-500);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .custom-resize-handle:hover::after,
  .custom-resize-handle:active::after {
    opacity: 1;
  }
}

/* 自定义 Typography 间距 - 减少元素间距 */
@layer utilities {
  /* 基础 prose 类的间距调整 */
  .prose {
    --tw-prose-body: var(--color-foreground);
    --tw-prose-headings: var(--color-foreground);
    --tw-prose-links: var(--color-primary);
    --tw-prose-bold: var(--color-foreground);
    --tw-prose-code: var(--color-foreground);
    --tw-prose-pre-code: var(--color-background);
    --tw-prose-pre-bg: var(--color-muted);
    --tw-prose-quotes: var(--color-foreground);
    --tw-prose-quote-borders: var(--color-border);
    --tw-prose-captions: var(--color-muted-foreground);
    --tw-prose-th-borders: var(--color-border);
    --tw-prose-td-borders: var(--color-border);
  }

  .prose p {
    margin-top: 0.375em;
    margin-bottom: 0.375em;
  }

  .prose h1 {
    margin-top: 0.5em;
    margin-bottom: 0.25em;
  }

  .prose h2 {
    margin-top: 0.75em;
    margin-bottom: 0.25em;
  }

  .prose h3 {
    margin-top: 0.625em;
    margin-bottom: 0.25em;
  }

  .prose h4 {
    margin-top: 0.5em;
    margin-bottom: 0.25em;
  }

  .prose ul,
  .prose ol {
    margin-top: 0.375em;
    margin-bottom: 0.375em;
  }

  .prose li {
    margin-top: 0.0625em;
    margin-bottom: 0.0625em;
  }

  .prose blockquote {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .prose pre {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .prose table {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .prose hr {
    margin-top: 1em;
    margin-bottom: 1em;
    background-color: var(--color-neutral-500) !important;
  }

  /* prose-sm 的更紧凑间距 */
  .prose-sm p {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  .prose-sm h1 {
    margin-top: 0.375em;
    margin-bottom: 0.1875em;
  }

  .prose-sm h2 {
    margin-top: 0.5em;
    margin-bottom: 0.1875em;
  }

  .prose-sm h3 {
    margin-top: 0.4375em;
    margin-bottom: 0.1875em;
  }

  .prose-sm h4 {
    margin-top: 0.375em;
    margin-bottom: 0.1875em;
  }

  .prose-sm ul,
  .prose-sm ol {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
    padding-inline-start: 0.4em;
  }

  .prose-sm li {
    margin-top: 0.03125em;
    margin-bottom: 0.03125em;
  }

  .prose-sm blockquote {
    margin-top: 0.375em;
    margin-bottom: 0.375em;
  }

  .prose-sm pre {
    margin-top: 0.375em;
    margin-bottom: 0.375em;
  }

  .prose-sm table {
    margin-top: 0.375em;
    margin-bottom: 0.375em;
  }

  .prose-sm hr {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
  }
}

/* Custom shadow effects */
@layer components {
  .shadow-around {
    box-shadow: 
      0 0 2px 0 rgba(0, 0, 0, 0.01),
      0 0 1px 0 rgba(0, 0, 0, 0.02),
      1px 1px 2px 0 rgba(0, 0, 0, 0.03);
  }

  .app-background {
    /* Light frosted overlay */
    background-color: oklch(0.999999993473546 0 none / 0.7);
  }

  /* Dark frosted overlay */
  .dark .app-background {
    background-color: oklch(0.2 0 none / 0.35);
  }

  .dark .shadow-around {
    box-shadow: 
      0 0 6px 0 rgba(0, 0, 0, 0.12),
      0 0 3px 0 rgba(0, 0, 0, 0.10),
      1px 1px 3px 0 rgba(0, 0, 0, 0.15);
  }
}
