import { useActiveBookStore } from "@/store/activeBookStore";
import { useLlamaStore } from "@/store/llamaStore";
import { invoke } from "@tauri-apps/api/core";
import { tool } from "ai";
import { z } from "zod";

// 获取向量模型配置的工具函数
function getVectorModelConfig() {
  const llamaState = useLlamaStore.getState();
  console.log("llamaState", llamaState);
  // 如果启用了向量模型功能且有选中的模型，优先使用
  if (llamaState.vectorModelEnabled) {
    const selectedModel = llamaState.getSelectedVectorModel();
    if (selectedModel) {
      return {
        baseUrl: selectedModel.url,
        model: selectedModel.modelId,
        apiKey: selectedModel.apiKey || null,
        dimension: 1024, // 保持默认维度
        source: "external",
      };
    }
  }

  // 回退到本地 llama.cpp 服务
  const port = llamaState.currentSession?.port;
  const baseUrl = port ? `http://127.0.0.1:${port}` : "http://127.0.0.1:3544";

  return {
    baseUrl,
    model: "local-embed",
    apiKey: null,
    dimension: 1024,
    source: "local",
  };
}

// 增强的搜索结果类型，包含位置信息
type EnhancedSearchItem = {
  book_title: string;
  book_author: string;
  chapter_title: string;
  content: string;
  similarity: number;

  // 位置信息，用于智能上下文检索
  chunk_id: number | null;
  toc_id: string;
  toc_depth: number;
  global_chunk_index: number;
  chunk_index_in_toc: number;
  total_chunks_in_toc: number;
  md_src: string;
};

// 统一的RAG搜索工具：向量检索 + 位置信息 + 可选格式化
export const ragSearchTool = tool({
  description: "在当前图书中进行向量检索，返回相关片段及其精确位置信息，支持后续的智能上下文检索",
  inputSchema: z.object({
    reasoning: z.string().min(1).describe("调用此工具的原因和目的，例如：'用户询问关于XX的问题，需要搜索相关内容'"),
    question: z.string().min(1).describe("用户的问题，将进行向量化相似度检索"),
    limit: z.number().int().min(1).max(20).default(3).describe("返回的片段数量，默认3个"),
    format: z.boolean().default(true).describe("是否返回格式化的上下文文本，默认true"),
  }),
  execute: async ({
    reasoning,
    question,
    limit,
    format,
  }: {
    reasoning: string;
    question: string;
    limit?: number;
    format?: boolean;
  }) => {
    const { activeBookId } = useActiveBookStore.getState();
    if (!activeBookId) {
      throw new Error("未找到当前阅读图书，请先在阅读器中打开图书");
    }

    console.log("执行ragSearchTool");

    // 获取向量模型配置（优先使用外部配置，回退到本地服务）
    const vectorConfig = getVectorModelConfig();

    const results = (await invoke("plugin:epub|search_db", {
      bookId: activeBookId,
      query: question,
      limit: limit ?? 5,
      dimension: vectorConfig.dimension,
      baseUrl: vectorConfig.baseUrl,
      model: vectorConfig.model,
      apiKey: vectorConfig.apiKey,
    })) as EnhancedSearchItem[];

    // 结构化结果，包含完整位置信息
    const enhancedContext = results.map((r, idx) => ({
      // 基础信息
      rank: idx + 1,
      chapter_title: r.chapter_title,
      similarity: Number.parseFloat((r.similarity * 100).toFixed(1)),
      content: r.content,

      // 关键位置信息，用于后续上下文检索
      position: {
        chunk_id: r.chunk_id,
        toc_id: r.toc_id,
        toc_depth: r.toc_depth,
        global_index: r.global_chunk_index,
        toc_position: `${r.chunk_index_in_toc + 1}/${r.total_chunks_in_toc}`,
        md_source: r.md_src,
      },
    }));

    // 可选的格式化输出（保持向后兼容）
    let formattedText = "";
    if (format) {
      const lines: string[] = [];
      lines.push(`[RAG检索结果] 找到 ${results.length} 个相关片段，包含位置信息：`);
      lines.push(`💭 调用原因：${reasoning}\n`);

      enhancedContext.forEach((item) => {
        const pos = item.position;
        lines.push(`【${item.rank} | 相似度${item.similarity}%】`);
        lines.push(`章节：${item.chapter_title}`);
        lines.push(`位置：TOC-${pos.toc_id} 第${pos.toc_position}块 (深度${pos.toc_depth}, 全局${pos.global_index})`);

        // 控制片段长度
        const snippet = item.content.length > 800 ? `${item.content.slice(0, 800)}…` : item.content;
        lines.push(snippet);
        lines.push("---\n");
      });

      formattedText = lines.join("\n");
    }

    // 生成标准化引用信息
    const citations = enhancedContext.map((item, idx) => ({
      id: idx + 1, // 引用序号 [1], [2] 等
      source: `${item.chapter_title} - 相似度${item.similarity}%`,
      toc_id: item.position.toc_id,
      position: `TOC-${item.position.toc_id} 第${item.position.toc_position}块`,
      preview: item.content.slice(0, 100) + (item.content.length > 100 ? "..." : ""),
    }));

    // 生成引用提示文本（供 AI 使用）
    const citationGuide = [
      "📚 引用标注指南：",
      "在回答中引用相关信息时，请在句子末尾添加对应的引用标注：",
      ...citations.map((c) => `[${c.id}] ${c.source} (${c.position})`),
      "",
      "示例：「根据书中描述，这个概念很重要[1]。相关原理如下[2]」",
    ].join("\n");

    return {
      // 增强的结构化数据，包含位置信息
      results: enhancedContext,
      // 格式化文本（如果需要）
      formatted: format ? formattedText : null,
      // ✨ 新增：标准化引用信息
      citations: citations,
      // ✨ 新增：引用指南（供 AI 参考）
      citation_guide: citationGuide,
      // 元信息
      meta: {
        reasoning,
        total_found: results.length,
        book_id: activeBookId,
        query: question,
      },
    };
  },
});
