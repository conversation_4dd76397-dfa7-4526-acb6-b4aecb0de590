# RAG工具系统文档

## 概述

本项目提供了一套完整的RAG（检索增强生成）工具系统，用于在电子书阅读过程中进行智能的内容检索和上下文分析。所有工具都基于当前打开的电子书进行操作，无需手动指定书籍ID。

## 🎯 设计理念

### 1. 透明度优先
- **reasoning参数**：所有工具都需要提供调用原因，让用户清楚地了解AI为什么调用特定工具
- **可视化展示**：工具调用时会显示💭图标和斜体的调用原因

### 2. 分层检索策略
- **第一步**：使用`ragSearch`进行精准的向量相似度检索
- **第二步**：根据需求选择扩展工具（`ragContext`、`ragToc`、`ragRange`）

### 3. 智能上下文管理
- 每个工具都返回结构化的位置信息
- 支持工具间的无缝协作
- 提供格式化和原始数据两种输出方式

## 🛠️ 工具列表

| 工具名称 | 主要用途 | 适用场景 |
|---------|---------|----------|
| ragSearch | 向量相似度检索 | 查找与问题相关的内容片段 |
| ragContext | 上下文扩展 | 获取特定分块的前后文 |
| ragToc | 章节完整检索 | 获取整个章节的所有内容 |
| ragRange | 范围连续检索 | 获取指定索引范围的连续内容 |

## 🔄 标准使用流程

```
用户问题 
    ↓
ragSearch (搜索相关内容)
    ↓
分析结果是否充分
    ↓
[不充分] → 选择扩展策略:
    ├── ragContext (需要更多上下文)
    ├── ragToc (需要完整章节)  
    └── ragRange (需要连续范围)
    ↓
基于内容生成回答
```

## 📊 工具调用统计

- **高频使用**：ragSearch (必需的第一步)
- **中频使用**：ragContext (上下文理解)、ragToc (章节讨论)
- **低频使用**：ragRange (特殊范围需求)

## 🎨 界面展示特性

### 工具卡片组件
- **头部**：工具名称 + 状态徽章
- **reasoning显示**：斜体文字在卡片外部显示调用原因
- **Input部分**：显示除reasoning外的所有参数
- **Output部分**：结构化的检索结果

### 交互体验
- 折叠/展开功能
- 状态可视化（处理中、完成、错误）
- 长文本截断和滚动

## 📁 文档结构

- [`rag-search.md`](./rag-search.md) - 向量检索工具详细文档
- [`rag-context.md`](./rag-context.md) - 上下文扩展工具详细文档
- [`rag-toc.md`](./rag-toc.md) - 章节检索工具详细文档
- [`rag-range.md`](./rag-range.md) - 范围检索工具详细文档
- [`prompt-integration.md`](./prompt-integration.md) - 提示词集成说明

## 🔧 技术实现

### 核心特性
- 基于AI SDK的工具定义
- Zod schema验证
- TypeScript类型安全
- Tauri后端集成

### 数据流转
```
Frontend (React) 
    ↓
AI SDK Tools
    ↓  
Tauri Commands
    ↓
Rust Backend
    ↓
SQLite Vector Database
```

## 🎯 使用建议

### For AI Assistant
1. **总是从ragSearch开始**
2. **提供清晰的reasoning**
3. **基于实际内容回答**
4. **避免过度调用工具**

### For Developer
1. **保持工具接口的一致性**
2. **优化检索性能**
3. **完善错误处理**
4. **监控使用情况**

---

> 本文档将随着工具系统的演进持续更新。如有疑问或建议，请查阅具体工具的详细文档。 